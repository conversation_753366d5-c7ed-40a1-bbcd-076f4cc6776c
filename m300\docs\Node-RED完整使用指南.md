# Node-RED 完整使用指南

## 目录
1. [Node-RED 简介](#node-red-简介)
2. [安装与配置](#安装与配置)
3. [基础概念](#基础概念)
4. [核心节点详解](#核心节点详解)
5. [流程设计最佳实践](#流程设计最佳实践)
6. [常见应用场景](#常见应用场景)
7. [故障排除](#故障排除)
8. [进阶技巧](#进阶技巧)

---

## Node-RED 简介

### 什么是 Node-RED
Node-RED 是一个基于流程的编程工具，专门用于连接硬件设备、API 和在线服务。它提供了一个基于浏览器的编辑器，让用户可以通过拖拽的方式将节点连接起来，创建数据流。

### 主要特点
- **可视化编程**：通过拖拽节点和连线的方式创建流程
- **丰富的节点库**：内置大量常用节点，支持 HTTP、MQTT、数据库等
- **易于扩展**：可以创建自定义节点
- **实时部署**：修改后立即生效，无需重启
- **跨平台**：支持 Windows、Linux、macOS

### 适用场景
- IoT 设备连接和数据收集
- API 集成和数据转换
-自动化工作流
- 数据监控和报警
- 系统集成

---

## 安装与配置

### 系统要求
- Node.js 14.0 或更高版本
- 现代浏览器（Chrome、Firefox、Safari、Edge）

### 安装方法

#### 方法一：使用 npm 全局安装
```bash
npm install -g node-red
```

#### 方法二：使用 Docker
```bash
docker run -it -p 1880:1880 --name mynodered nodered/node-red
```

#### 方法三：使用包管理器
```bash
# Ubuntu/Debian
sudo apt-get install node-red

# macOS (使用 Homebrew)
brew install node-red
```

### 启动 Node-RED
```bash
node-red
```

启动后，在浏览器中访问 `http://localhost:1880` 即可打开 Node-RED 编辑器。

### 基本配置
Node-RED 的配置文件位于 `~/.node-red/settings.js`，可以配置：
- 端口号
- 安全设置
- 数据库连接
- 自定义节点路径

---

## 基础概念

### 节点 (Nodes)
节点是 Node-RED 的基本构建块，每个节点都有特定的功能：
- **输入节点**：接收数据（如 HTTP 请求、MQTT 消息）
- **处理节点**：转换或处理数据（如函数、模板）
- **输出节点**：发送数据（如 HTTP 响应、数据库写入）

### 流程 (Flows)
流程是节点的集合，定义了数据如何从一个节点流向另一个节点。

### 消息 (Messages)
消息是节点之间传递的数据对象，包含：
- `msg.payload`：主要数据内容
- `msg.topic`：主题标识
- `msg.timestamp`：时间戳
- 自定义属性

### 连线 (Wires)
连线定义了数据流的方向，连接输出端口到输入端口。

---

## 核心节点详解

### 输入节点

#### 1. HTTP 输入节点
**功能**：接收 HTTP 请求
**配置参数**：
- Method：HTTP 方法（GET、POST、PUT、DELETE）
- URL：请求路径
- 认证：可选的认证设置

**输出**：
- `msg.payload`：请求体数据
- `msg.req`：HTTP 请求对象
- `msg.res`：HTTP 响应对象

#### 2. MQTT 输入节点
**功能**：订阅 MQTT 主题
**配置参数**：
- Broker：MQTT 代理地址
- Topic：订阅的主题
- QoS：服务质量等级

**输出**：
- `msg.payload`：接收到的消息
- `msg.topic`：消息主题

#### 3. 定时器节点
**功能**：按时间间隔触发
**配置参数**：
- 间隔：触发间隔时间
- 类型：一次性、重复、随机

**输出**：
- `msg.payload`：当前时间戳

### 处理节点

#### 1. 函数节点
**功能**：执行 JavaScript 代码
**示例代码**：
```javascript
// 简单的数据处理
msg.payload = msg.payload.toUpperCase();
return msg;
```

#### 2. 模板节点
**功能**：使用模板生成文本
**模板示例**：
```
温度: {{payload.temperature}}°C
湿度: {{payload.humidity}}%
时间: {{timestamp}}
```

#### 3. 开关节点
**功能**：根据条件路由消息
**配置**：
- 条件：JavaScript 表达式
- 输出：多个输出端口

#### 4. 变更节点
**功能**：修改消息属性
**操作类型**：
- 设置：设置属性值
- 删除：删除属性
- 移动：移动属性位置

### 输出节点

#### 1. HTTP 响应节点
**功能**：发送 HTTP 响应
**配置**：
- 状态码：HTTP 状态码
- 头部：响应头

#### 2. MQTT 输出节点
**功能**：发布 MQTT 消息
**配置**：
- Broker：MQTT 代理
- Topic：发布主题
- QoS：服务质量

#### 3. 数据库节点
**功能**：数据库操作
**支持数据库**：
- MongoDB
- MySQL
- PostgreSQL
- SQLite

#### 4. 文件节点
**功能**：文件读写操作
**操作类型**：
- 读取文件
- 写入文件
- 追加文件

---

## 流程设计最佳实践

### 1. 流程组织
- 使用子流程组织相关节点
- 为流程和节点添加有意义的名称
- 使用注释节点说明流程功能

### 2. 错误处理
- 使用 Catch 节点捕获错误
- 实现适当的错误日志记录
- 提供错误恢复机制

### 3. 性能优化
- 避免在函数节点中进行耗时操作
- 使用批处理处理大量数据
- 合理使用缓存机制

### 4. 安全性
- 验证输入数据
- 使用环境变量存储敏感信息
- 实施适当的访问控制

### 5. 调试技巧
- 使用 Debug 节点输出调试信息
- 利用 Status 节点监控节点状态
- 使用 Inject 节点手动触发流程

---

## 常见应用场景

### 1. IoT 数据收集
```
传感器 → MQTT → 数据处理 → 数据库存储
```

**实现步骤**：
1. 配置 MQTT 输入节点接收传感器数据
2. 使用函数节点解析和验证数据
3. 通过数据库节点存储数据
4. 添加报警节点监控异常值

### 2. API 集成
```
HTTP 请求 → 数据转换 → 外部 API → 响应处理
```

**实现步骤**：
1. 使用 HTTP 输入节点接收请求
2. 通过函数节点转换数据格式
3. 使用 HTTP 请求节点调用外部 API
4. 用 HTTP 响应节点返回结果

### 3. 自动化工作流
```
定时器 → 条件判断 → 多个操作 → 通知
```

**实现步骤**：
1. 配置定时器节点定期触发
2. 使用开关节点根据条件分支
3. 执行相应的操作（邮件、短信等）
4. 发送通知确认完成

### 4. 数据监控和报警
```
数据源 → 阈值检查 → 报警触发 → 通知发送
```

**实现步骤**：
1. 连接数据源（数据库、API 等）
2. 使用函数节点检查阈值
3. 通过开关节点判断是否需要报警
4. 使用通知节点发送报警信息

---

## 故障排除

### 常见问题

#### 1. 节点无法连接
**可能原因**：
- 网络连接问题
- 配置参数错误
- 服务未启动

**解决方法**：
- 检查网络连接
- 验证配置参数
- 确认服务状态

#### 2. 流程不执行
**可能原因**：
- 节点配置错误
- 连线断开
- 权限问题

**解决方法**：
- 检查节点配置
- 重新连接连线
- 检查权限设置

#### 3. 性能问题
**可能原因**：
- 流程过于复杂
- 数据处理效率低
- 资源不足

**解决方法**：
- 简化流程设计
- 优化数据处理逻辑
- 增加系统资源

### 调试工具
- **Debug 节点**：输出消息内容
- **Status 节点**：显示节点状态
- **Inject 节点**：手动触发流程
- **日志查看器**：查看系统日志

---

## 进阶技巧

### 1. 自定义节点开发
```javascript
module.exports = function(RED) {
    function MyNode(config) {
        RED.nodes.createNode(this, config);
        var node = this;
        
        this.on('input', function(msg) {
            // 节点逻辑
            node.send(msg);
        });
    }
    
    RED.nodes.registerType("my-node", MyNode);
}
```

### 2. 环境变量使用
```javascript
// 在函数节点中使用环境变量
var apiKey = process.env.API_KEY;
var baseUrl = process.env.BASE_URL;
```

### 3. 全局上下文
```javascript
// 设置全局变量
context.global.set("counter", 0);

// 获取全局变量
var count = context.global.get("counter");
```

### 4. 流程导入导出
- 导出：选择流程 → 导出 → 复制到剪贴板
- 导入：导入 → 从剪贴板粘贴

### 5. 版本控制
- 将流程文件存储在 Git 仓库中
- 使用环境变量管理配置
- 定期备份流程数据

---

## 总结

Node-RED 是一个强大的可视化编程工具，特别适合 IoT 和系统集成场景。通过掌握基础概念、熟悉核心节点、遵循最佳实践，您可以快速构建复杂的数据流和自动化系统。

### 学习建议
1. 从简单的流程开始练习
2. 多使用 Debug 节点调试
3. 参考官方文档和社区示例
4. 逐步尝试复杂场景
5. 参与社区讨论和分享

### 资源链接
- [Node-RED 官方网站](https://nodered.org/)
- [Node-RED 文档](https://nodered.org/docs/)
- [Node-RED 社区](https://discourse.nodered.org/)
- [Node-RED 节点库](https://flows.nodered.org/)

---

*本文档基于 Node-RED 最新版本编写，如有疑问请参考官方文档或社区支持。* 
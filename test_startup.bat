@echo off
chcp 65001
echo ========================================
echo 水利站数据接收流程测试
echo ========================================
echo.

echo 1. 检查Docker服务状态...
cd /d F:\水利站
docker-compose ps

echo.
echo 2. 启动RabbitMQ服务（如果未启动）...
docker-compose up rabbitmq -d

echo.
echo 等待RabbitMQ启动完成...
timeout /t 10

echo.
echo 3. 现在请按以下顺序手动启动程序：
echo.
echo 第一步：启动主程序
echo    cd F:\水利站\backend
echo    python main.py
echo.
echo 第二步：启动日志处理器（新开命令行窗口）
echo    cd F:\水利站\backend  
echo    python log_processor.py
echo.
echo 第三步：运行快速测试（新开命令行窗口）
echo    cd F:\水利站\backend
echo    python quick_test.py
echo.
echo 第四步：运行完整仿真测试（可选）
echo    cd F:\水利站
echo    python simulation_test.py --duration 60
echo.

echo ========================================
echo 状态变化频率已调整：
echo - 数据破损概率: 10%% → 2%%
echo - DO状态变化概率: 10%% → 2%%
echo - 水泵档位变化概率: 5%% → 1%%
echo ========================================
echo.

pause
# 水利站后端系统依赖项
# MongoDB + MySQL + RabbitMQ 架构所需依赖

# 现有依赖项（保持不变）
fastapi==0.104.1
uvicorn==0.24.0
sqlalchemy==2.0.23
pydantic==2.5.0
APScheduler==3.10.4

# MongoDB 相关依赖
pymongo==4.6.0
motor==3.3.2  # 异步MongoDB驱动（如果需要异步支持）

# MySQL 相关依赖
PyMySQL==1.1.0
# mysqlclient==2.2.0  # Windows上编译困难，使用PyMySQL代替

# RabbitMQ 相关依赖
pika==1.3.2  # RabbitMQ Python客户端

# 数据处理依赖
schedule==1.2.0
pandas==2.1.4  # 用于数据分析和降采样（可选）

# 日志和监控
python-json-logger==2.0.7

# 开发和测试依赖
pytest==7.4.3
pytest-asyncio==0.21.1
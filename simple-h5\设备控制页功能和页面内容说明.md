# 水利智能管理平台 - 设备控制页功能和页面内容说明

## 页面概述

设备控制页是水利智能管理平台的核心页面，提供实时设备监控、手动控制、任务调度等主要功能。页面采用移动端优化的卡片式布局，用户可以通过直观的界面监控和控制4台设备（水泵1、水泵2、气泵1、气泵2）。

## 核心功能模块

### 1. 实时设备监控

#### 设备状态监控

- **4台设备监控**: 水泵1、水泵2、气泵1、气泵2
- **运行状态显示**: 实时显示每台设备的运行/停止状态
- **电流监测**: 显示每台设备的实时电流值(精确到0.001A)
- **旋钮档位显示**: 可视化显示设备当前工作模式
  - 🔵 **自动模式**: 可接受系统调度控制
  - ⭕ **停止模式**: 设备强制停止
  - 🟢 **手动模式**: 人工现场操作

#### 环境传感器监控

- **浮球开关**: 水位传感器状态监控(激活/未激活)
- **温度监测**: 实时环境温度显示(°C)
- **湿度监测**: 实时环境湿度显示(%)

#### 电力参数监控

- **三相电压**: A相、B相、C相电压值
- **三相电流**: A相、B相、C相电流值
- **总有功功率**: 系统总功率消耗
- **用电度数**: 累计用电量统计

### 2. 设备控制功能

#### DO直接控制

- **4路数字输出控制**:
  - DO21 → 水泵1
  - DO22 → 水泵2
  - DO23 → 气泵1
  - DO24 → 气泵2
- **即时控制**: 一键开启/关闭设备
- **状态反馈**: 控制命令执行结果实时反馈

### 3. 智能任务调度系统

#### 单次任务

**功能描述**: 延时执行一次性设备控制操作

- **参数设置**:
  - 选择目标设备(DO21-DO24)
  - 选择操作类型(开启/关闭)
  - 设置延迟时间(分钟)
- **智能验证**: 自动检查设备是否处于自动模式
- **执行监控**: 显示任务执行时间和状态

#### 循环任务

**功能描述**: 设备按设定周期自动开启和关闭

- **参数设置**:
  - 选择目标设备
  - 设置开启持续时间(分钟)
  - 设置关闭持续时间(分钟)
- **执行特点**: 无限循环执行直到手动取消
- **应用场景**: 定时曝气、间歇运行等

#### 顺序任务

**功能描述**: 两个设备交替运行的循环模式

- **参数设置**:
  - 选择设备A和设备B
  - 分别设置两个设备的运行时长
- **执行逻辑**: A运行→A停止+B启动→B停止+A启动→循环
- **应用场景**: 水泵轮换、设备交替工作

#### 水泵配置管理

**功能描述**: 水泵自动轮换时间配置

- **配置参数**: 水泵1运行多久后自动启动水泵2
- **默认设置**: 2小时轮换间隔
- **管理功能**:
  - 自定义延时时间
  - 一键重置为默认值
  - 实时配置状态显示

## 页面结构和内容

### 页面头部区域

- **应用标题**: "设备控制"
- **导航按钮**: "查看历史数据" 入口链接

### 设备控制区域

每个设备显示卡片包含:

- **设备名称**: 水泵1/水泵2/气泵1/气泵2
- **运行状态指示**:
  - 🟢 运行中 (绿色)
  - 🔴 已停止 (红色)
- **实时电流值**: 显示当前电流消耗
- **旋钮档位显示**: 三段式开关状态可视化
  - 自动/停止/手动模式高亮显示
- **操作说明**: "如需操作请使用DO直控或定时任务功能"

### 传感器数据区域

#### 浮球状态卡片

- 显示当前浮球开关状态
- 激活/未激活状态标识

#### 温湿度卡片

- 温度值显示(°C)
- 湿度值显示(%)
- 大数字显示格式

#### 电能表卡片

- A/B/C相电流值列表
- 总有功功率显示
- 用电度数累计值

### DO直控操作区域

4行设备控制项，每行包含:

- **设备标签**: 水泵1/水泵2/气泵1/气泵2
- **开启按钮**: 绿色按钮，发送开启命令
- **关闭按钮**: 红色按钮，发送关闭命令

### 任务调度区域

#### 任务类型切换器

- 单次任务 | 循环任务 | 顺序任务 | 水泵配置

#### 单次任务表单

- **DO选择下拉框**: 选择要控制的设备
- **操作类型选择**: 开启/关闭单选
- **延迟时间输入框**: 输入分钟数
- **添加任务按钮**: 创建任务
- **表单验证提示**: 显示验证错误信息

#### 循环任务表单

- **DO选择下拉框**: 选择要控制的设备
- **开启时长输入框**: 设备开启持续时间(分钟)
- **关闭时长输入框**: 设备关闭持续时间(分钟)
- **启动循环任务按钮**: 开始循环执行
- **表单验证提示**: 显示验证错误信息

#### 顺序任务表单

- **设备A选择下拉框**: 选择第一个设备
- **设备A运行时长输入框**: 设备A的运行时间
- **设备B选择下拉框**: 选择第二个设备
- **设备B运行时长输入框**: 设备B的运行时间
- **启动顺序任务按钮**: 开始顺序执行
- **任务说明**: "A运行指定分钟后关闭，并立即启动B，B运行完后重新启动A，无限循环"
- **表单验证提示**: 显示验证错误信息

#### 水泵配置表单

- **延时时间输入框**: 水泵2启动延时(小时)
- **更新配置按钮**: 保存配置更改
- **重置为默认值按钮**: 恢复默认2小时设置
- **配置说明**: "设置水泵1运行多久后自动启动水泵2（默认2小时）"
- **表单验证提示**: 显示验证错误信息

### 活动任务监控区域

- **区域标题**: "当前活动任务"
- **任务列表**: 显示所有当前运行中的任务
  - **单次任务**: 显示设备名称、操作类型、执行时间
  - **循环任务**: 显示设备名称、开启时长、关闭时长
  - **顺序任务**: 显示两个设备名称和各自运行时长
  - **水泵配置信息**: 显示当前配置的延时时间
- **取消按钮**: 每个任务都有独立的取消按钮
- **空状态提示**: "当前没有活动任务"

### 设备信息区域

- **区域标题**: "设备信息"
- **设备SN**: 设备序列号
- **固件版本**: 当前固件版本号
- **IMEI**: 设备IMEI信息
- **数据时间戳**: 最后更新时间(中国时区格式)

## 用户交互流程

### 设备监控流程

1. 用户打开设备控制页，自动加载设备数据
2. 系统每30秒自动刷新数据
3. 用户查看各设备状态和传感器数据
4. 异常状态会通过颜色变化提醒用户

### 设备控制流程

1. 用户在DO直控区域选择要控制的设备
2. 点击开启/关闭按钮
3. 系统发送控制命令到设备
4. 显示执行结果(成功/失败提示)
5. 设备状态实时更新

### 任务调度流程

1. 用户选择任务类型(单次/循环/顺序/配置)
2. 填写任务参数(设备、时间等)
3. 系统验证设备是否处于自动模式
4. 验证通过后创建任务
5. 任务出现在活动任务列表
6. 用户可随时取消正在运行的任务

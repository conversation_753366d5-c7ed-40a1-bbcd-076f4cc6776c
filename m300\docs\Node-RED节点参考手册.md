# Node-RED 节点参考手册

## 目录
1. [输入节点](#输入节点)
2. [输出节点](#输出节点)
3. [处理节点](#处理节点)
4. [功能节点](#功能节点)
5. [网络节点](#网络节点)
6. [存储节点](#存储节点)
7. [解析节点](#解析节点)
8. [序列节点](#序列节点)

---

## 输入节点

### Inject 节点
**功能**：手动触发流程或定时触发

**配置参数**：
- **Name**：节点名称
- **Payload**：发送的数据内容
  - String：字符串
  - Number：数字
  - Boolean：布尔值
  - JSON：JSON对象
  - Date object：日期对象
  - Timestamp：时间戳
- **Topic**：消息主题
- **Repeat**：重复间隔
  - None：不重复
  - Interval：固定间隔
  - Random interval：随机间隔
- **Once at**：指定时间执行一次

**输出**：
```javascript
{
    payload: "配置的数据",
    topic: "配置的主题",
    timestamp: 1234567890
}
```

**使用示例**：
```javascript
// 配置为发送传感器数据
{
    payload: {
        temperature: 25.5,
        humidity: 60,
        timestamp: new Date().toISOString()
    },
    topic: "sensor/data"
}
```

### HTTP in 节点
**功能**：接收HTTP请求

**配置参数**：
- **Name**：节点名称
- **Method**：HTTP方法（GET、POST、PUT、DELETE等）
- **URL**：请求路径（如 `/api/data`）
- **Upload**：文件上传设置
- **Authentication**：认证设置
- **TLS**：TLS配置

**输出**：
```javascript
{
    payload: "请求体数据",
    req: "HTTP请求对象",
    res: "HTTP响应对象",
    headers: "请求头",
    query: "查询参数"
}
```

**使用示例**：
```javascript
// 接收POST请求
// URL: /api/sensor
// Method: POST
// 接收JSON数据
{
    payload: {
        sensor_id: "temp001",
        value: 25.5
    }
}
```

### MQTT in 节点
**功能**：订阅MQTT主题

**配置参数**：
- **Name**：节点名称
- **Broker**：MQTT代理配置
- **Topic**：订阅主题（支持通配符）
- **QoS**：服务质量（0、1、2）
- **Output**：输出格式
  - Auto-detect：自动检测
  - String：字符串
  - Buffer：二进制
  - JSON object：JSON对象
  - Base64：Base64编码

**输出**：
```javascript
{
    payload: "接收到的消息",
    topic: "消息主题",
    qos: 1,
    retain: false
}
```

### WebSocket in 节点
**功能**：接收WebSocket连接

**配置参数**：
- **Name**：节点名称
- **Path**：WebSocket路径
- **Whole msg**：是否输出完整消息对象

**输出**：
```javascript
{
    payload: "WebSocket消息",
    topic: "连接ID",
    client: "客户端对象"
}
```

---

## 输出节点

### Debug 节点
**功能**：调试输出，在右侧面板显示消息

**配置参数**：
- **Name**：节点名称
- **Output**：输出内容
  - Complete msg object：完整消息对象
  - Selected property：选择特定属性
- **To**：输出目标
  - Debug sidebar：调试侧边栏
  - Console log：控制台日志
  - Status：状态栏

**使用示例**：
```javascript
// 输出msg.payload
// 输出msg.topic
// 输出完整msg对象
```

### HTTP response 节点
**功能**：发送HTTP响应

**配置参数**：
- **Name**：节点名称
- **Status code**：HTTP状态码
- **Headers**：响应头
- **Response**：响应内容

**使用示例**：
```javascript
// 成功响应
{
    statusCode: 200,
    headers: {
        "Content-Type": "application/json"
    },
    payload: {
        success: true,
        data: "处理结果"
    }
}
```

### MQTT out 节点
**功能**：发布MQTT消息

**配置参数**：
- **Name**：节点名称
- **Broker**：MQTT代理配置
- **Topic**：发布主题
- **QoS**：服务质量
- **Retain**：是否保留消息
- **Payload**：消息内容

**使用示例**：
```javascript
// 发布传感器数据
{
    topic: "sensor/temperature",
    payload: JSON.stringify({
        value: 25.5,
        timestamp: new Date().toISOString()
    }),
    qos: 1
}
```

### File 节点
**功能**：文件读写操作

**配置参数**：
- **Name**：节点名称
- **Operation**：操作类型
  - Read file：读取文件
  - Write file：写入文件
  - Append to file：追加文件
- **Filename**：文件名
- **Encoding**：编码格式

**使用示例**：
```javascript
// 写入日志文件
{
    filename: "/var/log/app.log",
    operation: "append",
    payload: "日志内容\n"
}
```

---

## 处理节点

### Function 节点
**功能**：执行JavaScript代码

**配置参数**：
- **Name**：节点名称
- **Function**：JavaScript代码

**常用代码示例**：

#### 数据转换
```javascript
// 转换温度单位
var temp = msg.payload.temperature;
msg.payload.celsius = temp;
msg.payload.fahrenheit = (temp * 9/5) + 32;
return msg;
```

#### 数据验证
```javascript
// 验证传感器数据
var data = msg.payload;
if (data.temperature < -50 || data.temperature > 100) {
    node.error("温度值超出范围", msg);
    return null;
}
return msg;
```

#### 数据聚合
```javascript
// 聚合多个传感器数据
var sensors = context.get("sensors") || [];
sensors.push(msg.payload);
context.set("sensors", sensors);

if (sensors.length >= 10) {
    msg.payload = {
        average: sensors.reduce((a, b) => a + b.temperature, 0) / sensors.length,
        count: sensors.length
    };
    context.set("sensors", []);
    return msg;
}
return null;
```

### Switch 节点
**功能**：根据条件路由消息

**配置参数**：
- **Name**：节点名称
- **Property**：检查的属性
- **Conditions**：条件列表

**条件类型**：
- **==**：等于
- **!=**：不等于
- **<**：小于
- **<=**：小于等于
- **>**：大于
- **>=**：大于等于
- **regex**：正则表达式
- **expression**：JavaScript表达式

**使用示例**：
```javascript
// 根据温度值路由
// 条件1: msg.payload.temperature > 30 → 输出1（高温报警）
// 条件2: msg.payload.temperature < 10 → 输出2（低温报警）
// 默认: 输出3（正常温度）
```

### Change 节点
**功能**：修改消息属性

**配置参数**：
- **Name**：节点名称
- **Changes**：修改操作列表

**操作类型**：
- **Set**：设置属性值
- **Change**：修改属性值
- **Delete**：删除属性
- **Move**：移动属性

**使用示例**：
```javascript
// 设置新属性
// Set msg.topic = "processed"
// Set msg.timestamp = Date.now()

// 删除属性
// Delete msg.raw

// 移动属性
// Move msg.payload.temp → msg.temperature
```

### Template 节点
**功能**：使用模板生成文本

**配置参数**：
- **Name**：节点名称
- **Template**：模板内容
- **Output**：输出格式

**模板语法**：
```javascript
// 基本变量
温度: {{payload.temperature}}°C
湿度: {{payload.humidity}}%
时间: {{timestamp}}

// 条件判断
{{#if payload.alert}}
⚠️ 报警: {{payload.message}}
{{else}}
✅ 正常
{{/if}}

// 循环
{{#each payload.sensors}}
传感器 {{@index}}: {{temperature}}°C
{{/each}}
```

---

## 功能节点

### Delay 节点
**功能**：延迟消息传递

**配置参数**：
- **Name**：节点名称
- **Delay**：延迟时间
- **Rate limit**：速率限制
- **Drop**：丢弃策略

**使用场景**：
- 防止消息过载
- 模拟网络延迟
- 控制处理速率

### Queue 节点
**功能**：消息队列管理

**配置参数**：
- **Name**：节点名称
- **Max length**：最大队列长度
- **Timeout**：超时时间
- **Strategy**：队列策略

**队列策略**：
- **Drop oldest**：丢弃最旧消息
- **Drop newest**：丢弃最新消息
- **Block**：阻塞新消息

### Catch 节点
**功能**：捕获错误

**配置参数**：
- **Name**：节点名称
- **Scope**：捕获范围
- **Uncaught**：未捕获错误

**使用示例**：
```javascript
// 捕获特定节点的错误
// 输出错误信息
msg.payload = {
    error: msg.error.message,
    node: msg.error.source.id,
    timestamp: new Date().toISOString()
};
```

### Status 节点
**功能**：显示节点状态

**配置参数**：
- **Name**：节点名称
- **Scope**：状态范围
- **Status**：状态类型

**状态类型**：
- **Text**：文本状态
- **Dot**：点状态
- **Ring**：环形状态

---

## 网络节点

### HTTP request 节点
**功能**：发送HTTP请求

**配置参数**：
- **Name**：节点名称
- **Method**：HTTP方法
- **URL**：请求URL
- **Headers**：请求头
- **TLS**：TLS配置

**使用示例**：
```javascript
// 调用外部API
{
    method: "POST",
    url: "https://api.example.com/data",
    headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer " + context.global.get("api_token")
    },
    payload: {
        sensor_id: "temp001",
        value: 25.5
    }
}
```

### WebSocket out 节点
**功能**：发送WebSocket消息

**配置参数**：
- **Name**：节点名称
- **Path**：WebSocket路径
- **Client**：客户端选择

**使用示例**：
```javascript
// 发送实时数据
{
    payload: {
        type: "sensor_update",
        data: msg.payload
    }
}
```

---

## 存储节点

### MongoDB 节点
**功能**：MongoDB数据库操作

**配置参数**：
- **Name**：节点名称
- **Server**：MongoDB服务器
- **Database**：数据库名
- **Collection**：集合名
- **Operation**：操作类型

**操作类型**：
- **Insert**：插入文档
- **Update**：更新文档
- **Delete**：删除文档
- **Find**：查询文档

**使用示例**：
```javascript
// 插入传感器数据
{
    operation: "insert",
    payload: {
        sensor_id: "temp001",
        temperature: 25.5,
        timestamp: new Date()
    }
}
```

### MySQL 节点
**功能**：MySQL数据库操作

**配置参数**：
- **Name**：节点名称
- **Database**：数据库配置
- **Query**：SQL查询

**使用示例**：
```javascript
// 查询传感器数据
{
    query: "SELECT * FROM sensors WHERE sensor_id = ?",
    params: ["temp001"]
}
```

---

## 解析节点

### JSON 节点
**功能**：JSON数据解析

**配置参数**：
- **Name**：节点名称
- **Action**：操作类型
- **Property**：目标属性

**操作类型**：
- **Parse JSON**：解析JSON字符串
- **Stringify**：转换为JSON字符串

**使用示例**：
```javascript
// 解析JSON字符串
// Input: '{"temperature": 25.5, "humidity": 60}'
// Output: {temperature: 25.5, humidity: 60}
```

### XML 节点
**功能**：XML数据解析

**配置参数**：
- **Name**：节点名称
- **Action**：操作类型
- **Property**：目标属性

**操作类型**：
- **Parse XML**：解析XML
- **Build XML**：构建XML

---

## 序列节点

### Split 节点
**功能**：拆分消息数组

**配置参数**：
- **Name**：节点名称
- **Splits**：拆分数量
- **Key**：拆分键

**使用示例**：
```javascript
// 拆分传感器数组
// Input: [{id: 1, temp: 25}, {id: 2, temp: 26}]
// Output: 两个独立消息
```

### Join 节点
**功能**：合并消息

**配置参数**：
- **Name**：节点名称
- **Mode**：合并模式
- **Count**：消息数量
- **Timeout**：超时时间

**合并模式**：
- **Automatic**：自动合并
- **Manual**：手动合并
- **Key value**：键值合并

**使用示例**：
```javascript
// 合并多个传感器数据
// 等待所有传感器数据到达后合并
```

### Sort 节点
**功能**：排序消息

**配置参数**：
- **Name**：节点名称
- **Property**：排序属性
- **Order**：排序顺序

**使用示例**：
```javascript
// 按时间戳排序
// Property: timestamp
// Order: ascending
```

---

## 节点配置最佳实践

### 1. 命名规范
- 使用有意义的节点名称
- 包含功能描述
- 保持一致性

### 2. 错误处理
- 使用Catch节点捕获错误
- 提供错误恢复机制
- 记录错误日志

### 3. 性能优化
- 避免在Function节点中进行耗时操作
- 合理使用缓存
- 控制消息流量

### 4. 安全性
- 验证输入数据
- 使用环境变量存储敏感信息
- 实施访问控制

---

*更多节点信息请参考Node-RED官方文档和社区贡献的节点。* 
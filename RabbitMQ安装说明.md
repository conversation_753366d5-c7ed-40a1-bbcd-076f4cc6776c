# RabbitMQ 本地安装说明

由于Docker镜像拉取问题，我们提供本地安装RabbitMQ的解决方案：

## 方案一：使用Windows安装包

1. 下载Erlang（RabbitMQ依赖）：
   - 访问：https://www.erlang.org/downloads
   - 下载Windows版本并安装

2. 下载RabbitMQ：
   - 访问：https://www.rabbitmq.com/install-windows.html
   - 下载Windows安装包并安装

3. 启动RabbitMQ：
   ```cmd
   # 安装管理插件
   rabbitmq-plugins enable rabbitmq_management
   
   # 启动服务
   rabbitmq-server
   ```

4. 创建用户：
   ```cmd
   # 添加用户
   rabbitmqctl add_user water_user water123
   
   # 设置管理员权限
   rabbitmqctl set_user_tags water_user administrator
   
   # 设置权限
   rabbitmqctl set_permissions -p / water_user ".*" ".*" ".*"
   ```

## 方案二：使用Chocolatey安装（推荐）

```powershell
# 安装Chocolatey（如果没有）
Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))

# 安装RabbitMQ
choco install rabbitmq

# 启动服务
rabbitmq-service start

# 启用管理插件
rabbitmq-plugins enable rabbitmq_management
```

## 配置完成后

1. 访问管理界面：http://localhost:15672
2. 默认用户名密码：guest/guest（本地访问）
3. 或使用我们创建的：water_user/water123

## 验证连接

运行以下Python代码验证连接：

```python
import pika

try:
    connection = pika.BlockingConnection(
        pika.ConnectionParameters('localhost')
    )
    channel = connection.channel()
    print("RabbitMQ连接成功！")
    connection.close()
except Exception as e:
    print(f"连接失败：{e}")
```

安装完成后，你就可以正常运行：
- `python main.py` (主程序)
- `python log_processor.py` (日志处理器)
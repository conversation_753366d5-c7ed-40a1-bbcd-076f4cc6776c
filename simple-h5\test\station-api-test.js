// 站点API测试脚本
console.log('开始测试站点API接口...')

// 1. 测试JWT Token解码
function testTokenDecoding() {
  console.log('\n=== 测试JWT Token解码 ===')
  
  const token = "eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIyMXhkOXU3MCIsInVpZCI6MjgzMTM3LCJ1c2VyTm8iOiJDTjAyMjQzNDAiLCJhdXRoIjoxLCJleHAiOjE3NTQzODA3MTcsImlhdCI6MTc1NDM3MzUxN30.Mgy06MJU0axukOXjs7Vqi9iXziwsAJi08qijOTRC0ls"
  
  try {
    // 解码JWT payload
    const payload = JSON.parse(atob(token.split('.')[1]))
    console.log('✅ JWT Token解码成功:')
    console.log('  用户ID:', payload.uid)
    console.log('  用户编号:', payload.userNo)
    console.log('  主题:', payload.sub)
    console.log('  权限:', payload.auth)
    console.log('  签发时间:', new Date(payload.iat * 1000).toLocaleString())
    console.log('  过期时间:', new Date(payload.exp * 1000).toLocaleString())
    
    // 检查是否过期
    const currentTime = Math.floor(Date.now() / 1000)
    const isValid = payload.exp > currentTime
    console.log('  Token状态:', isValid ? '🟢 有效' : '🔴 已过期')
    
    return { valid: isValid, payload }
  } catch (error) {
    console.error('❌ JWT Token解码失败:', error.message)
    return { valid: false, error: error.message }
  }
}

// 2. 测试登录API
async function testLoginAPI() {
  console.log('\n=== 测试登录API ===')
  
  const LOGIN_URL = 'https://accountapi.usr.cn/api/Login/loginByPassword'
  const USERNAME = '***********'
  const PASSWORD = 'd33f11e1b1f613d4e295d77a23c04ef4'
  const PLATFORM_ID = 'DmPub'

  try {
    const response = await fetch(LOGIN_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
        'Accept': '*/*',
        'Origin': 'https://dm.usr.cn',
        'Referer': 'https://dm.usr.cn/'
      },
      body: JSON.stringify({
        username: USERNAME,
        password: PASSWORD,
        platformId: PLATFORM_ID
      })
    })

    console.log('📡 登录请求状态:', response.status)
    const result = await response.json()
    
    if (result.status === 0) {
      console.log('✅ 登录成功:')
      console.log('  账户:', result.data.account)
      console.log('  用户名:', result.data.fullName || '未设置')
      console.log('  Token:', result.data.token ? '已获取' : '未获取')
      return { success: true, data: result.data }
    } else {
      console.log('❌ 登录失败:')
      console.log('  错误信息:', result.info)
      return { success: false, error: result.info }
    }
  } catch (error) {
    console.error('❌ 登录请求异常:', error.message)
    return { success: false, error: error.message }
  }
}

// 3. 测试站点列表API
async function testStationListAPI(token) {
  console.log('\n=== 测试站点列表API ===')
  
  const API_URL = 'https://api-dm.usr.cn/dmCloud/dev/getDevs'
  
  const requestBody = {
    appointLoraNodeDevice: 1,
    appointSubNodeDevice: 2, 
    devicesTagParamDtos: [],
    pageNo: 1,
    pageSize: 10,
    projectId: "",
    searchParam: "",
    sortByWeight: "up",
    token: token
  }

  const headers = {
    'Content-Type': 'application/json',
    'Accept': '*/*',
    'Accept-Language': 'zh-CN,zh;q=0.9',
    'Origin': 'https://dm.usr.cn',
    'Referer': 'https://dm.usr.cn/',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
    'appid': 'cG8yNDYwODl6a2xqYWdpdXNkZ3E=',
    'languagetype': '0',
    'token': token,
    'traceid': '283137',
    'u-source': 'in-pc',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-site'
  }

  try {
    console.log('📡 发送站点列表请求...')
    const response = await fetch(API_URL, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(requestBody)
    })

    console.log('📡 响应状态:', response.status)
    console.log('📡 响应头 Access-Control-Allow-Origin:', response.headers.get('Access-Control-Allow-Origin'))
    
    const result = await response.json()
    
    if (result.status === 0 && result.data) {
      console.log('✅ 站点列表获取成功:')
      console.log('  总数量:', result.data.total)
      console.log('  当前页设备数:', result.data.list.length)
      
      if (result.data.list.length > 0) {
        console.log('\n📋 站点详细信息:')
        result.data.list.forEach((device, index) => {
          console.log(`  ${index + 1}. ${device.deviceName || device.projectName}`)
          console.log(`     SN: ${device.sn}`)
          console.log(`     项目: ${device.projectName}`)
          console.log(`     型号: ${device.deviceModelName}`)
          console.log(`     地址: ${device.deviceAddress || '未设置'}`)
          console.log(`     在线状态: ${device.deviceStatus.onlineOffline === 1 ? '🟢 在线' : '🔴 离线'}`)
          console.log('')
        })
        
        // 查找我们关心的设备
        const targetDevices = ['02801925060700002997', '02800125071500004977']
        console.log('🔍 查找目标设备:')
        targetDevices.forEach(targetSN => {
          const device = result.data.list.find(d => d.sn === targetSN)
          if (device) {
            console.log(`  ✅ 找到设备 ${targetSN}:`)
            console.log(`     名称: ${device.deviceName || device.projectName}`)
            console.log(`     状态: ${device.deviceStatus.onlineOffline === 1 ? '在线' : '离线'}`)
          } else {
            console.log(`  ❌ 未找到设备 ${targetSN}`)
          }
        })
      }
      
      return { success: true, data: result.data }
    } else {
      console.log('❌ 站点列表获取失败:')
      console.log('  错误状态:', result.status)
      console.log('  错误信息:', result.info || '未知错误')
      return { success: false, error: result.info || '未知错误' }
    }
  } catch (error) {
    console.error('❌ 站点列表请求异常:', error.message)
    if (error.message.includes('CORS')) {
      console.log('💡 提示: 这可能是CORS跨域问题，在浏览器环境中可能被阻止')
    }
    return { success: false, error: error.message }
  }
}

// 4. 主测试函数
async function runTests() {
  try {
    // 测试1: JWT Token解码
    const tokenTest = testTokenDecoding()
    
    // 测试2: 登录API
    const loginTest = await testLoginAPI()
    
    // 测试3: 使用硬编码Token测试站点API
    console.log('\n--- 使用硬编码Token测试站点API ---')
    const hardcodedToken = "eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIyMXhkOXU3MCIsInVpZCI6MjgzMTM3LCJ1c2VyTm8iOiJDTjAyMjQzNDAiLCJhdXRoIjoxLCJleHAiOjE3NTQzODA3MTcsImlhdCI6MTc1NDM3MzUxN30.Mgy06MJU0axukOXjs7Vqi9iXziwsAJi08qijOTRC0ls"
    const stationTest1 = await testStationListAPI(hardcodedToken)
    
    // 测试4: 如果登录成功，使用新Token测试站点API
    if (loginTest.success && loginTest.data.token) {
      console.log('\n--- 使用新Token测试站点API ---')
      await testStationListAPI(loginTest.data.token)
    }
    
    // 总结测试结果
    console.log('\n🎯 测试总结:')
    console.log('  JWT解码:', tokenTest.valid ? '✅ 成功' : '❌ 失败')
    console.log('  登录API:', loginTest.success ? '✅ 成功' : '❌ 失败')
    console.log('  站点API:', stationTest1.success ? '✅ 成功' : '❌ 失败')
    
    if (stationTest1.success) {
      console.log('\n🎉 结论: 可以通过外部API获取真实站点数据!')
      console.log('   建议: 更新应用中的token或登录逻辑')
    } else {
      console.log('\n⚠️ 结论: 外部API暂时无法访问')
      console.log('   建议: 继续使用本地模拟数据')
    }
    
  } catch (error) {
    console.error('💥 测试过程中发生异常:', error)
  }
}

// 运行测试
runTests()
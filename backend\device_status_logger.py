# 设备状态变化日志记录模块
"""
专门用于记录设备物理开关档位变换导致的状态改变
这些状态变化作为设备操作日志记录到日志文件中，不存入MongoDB
"""

import os
import json
import datetime
import logging
from pathlib import Path
from typing import Dict, Any, Optional
import threading

class DeviceStatusLogger:
    """设备状态变化日志记录器"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self, logs_dir: str = "./logs/device_status"):
        """
        初始化设备状态变化日志记录器
        :param logs_dir: 日志文件存储目录
        """
        if hasattr(self, '_initialized'):
            return
            
        self.logs_dir = Path(logs_dir)
        self.logs_dir.mkdir(parents=True, exist_ok=True)
        
        # 设备状态缓存，用于检测状态变化
        self.device_status_cache = {}
        self.cache_lock = threading.Lock()
        
        # 当前日期和对应的日志记录器
        self._current_date = None
        self._current_logger = None
        self._logger_lock = threading.Lock()
        
        # 初始化当天的日志记录器
        self._setup_daily_logger()
        
        self._initialized = True
    
    def _setup_daily_logger(self):
        """设置当天的设备状态变化日志记录器"""
        today = datetime.date.today()
        
        # 如果日期发生变化，需要重新创建日志记录器
        if self._current_date != today or self._current_logger is None:
            # 生成日志文件名：device_status_YYYYMMDD.log
            log_filename = self.logs_dir / f"device_status_{today.strftime('%Y%m%d')}.log"
            
            # 创建新的日志记录器
            logger_name = f'device_status_{today.strftime("%Y%m%d")}'
            logger = logging.getLogger(logger_name)
            logger.setLevel(logging.INFO)
            
            # 清除旧的handler
            for handler in logger.handlers[:]:
                logger.removeHandler(handler)
            
            # 创建文件handler
            file_handler = logging.FileHandler(log_filename, encoding='utf-8')
            file_handler.setLevel(logging.INFO)
            
            # 创建formatter - 专门格式化设备状态变化日志
            formatter = logging.Formatter('%(asctime)s - %(message)s')
            file_handler.setFormatter(formatter)
            
            # 添加handler到logger
            logger.addHandler(file_handler)
            
            self._current_date = today
            self._current_logger = logger
            
            print(f"[设备状态日志] 初始化日志文件: {log_filename}")
    
    def _get_current_logger(self) -> logging.Logger:
        """
        获取当前日期的日志记录器，如果日期发生变化会自动切换
        :return: 当前日志记录器
        """
        with self._logger_lock:
            # 检查日期是否发生变化
            today = datetime.date.today()
            if self._current_date != today:
                self._setup_daily_logger()
            
            return self._current_logger
    
    def detect_and_log_status_changes(self, device_sn: str, new_data: Dict[str, Any], 
                                    client_address: Optional[str] = None, is_status_only: bool = False):
        """
        检测设备状态变化并记录到日志文件
        :param device_sn: 设备序列号
        :param new_data: 新的设备数据或状态变化数据
        :param client_address: 客户端地址（可选）
        :param is_status_only: 是否为单独的状态变化消息
        """
        try:
            with self.cache_lock:
                # 获取之前的状态
                previous_status = self.device_status_cache.get(device_sn, {})
                
                # 提取当前DO状态和相关开关状态（支持三档位）
                current_status = {}
                status_fields = [
                    'DO21_status', 'DO22_status', 'DO23_status', 'DO24_status',
                    'water_pump1', 'water_pump2', 'air_pump1', 'air_pump2'
                ]
                
                for field in status_fields:
                    if field in new_data:
                        if isinstance(new_data[field], dict):
                            # 支持新的三档位模式：mode_status (0=停止, 1=手动, 2=自动)
                            if 'mode_status' in new_data[field]:
                                current_status[field] = new_data[field]['mode_status']
                            # 兼容旧的二档位模式：auto_status (0=停止, 1=启动)
                            elif 'auto_status' in new_data[field]:
                                current_status[field] = new_data[field]['auto_status']
                            else:
                                current_status[field] = new_data[field]
                        else:
                            current_status[field] = new_data[field]
                
                # 如果是单独的状态变化消息，直接记录所有状态信息
                if is_status_only:
                    changes_detected = []
                    for field, current_value in current_status.items():
                        changes_detected.append({
                            'field': field,
                            'previous_value': previous_status.get(field, 'unknown'),
                            'current_value': current_value,
                            'change_type': self._get_change_type(field, previous_status.get(field), current_value),
                            'message_type': '物理开关档位变换'
                        })
                else:
                    # 检测状态变化（正常模式）
                    changes_detected = []
                    for field, current_value in current_status.items():
                        previous_value = previous_status.get(field)
                        
                        if previous_value is not None and previous_value != current_value:
                            changes_detected.append({
                                'field': field,
                                'previous_value': previous_value,
                                'current_value': current_value,
                                'change_type': self._get_change_type(field, previous_value, current_value),
                                'message_type': '状态数据变化'
                            })
                
                # 如果检测到状态变化，记录到日志
                if changes_detected:
                    log_type = '设备物理开关档位变换' if is_status_only else '设备状态数据变化'
                    self._log_status_changes(device_sn, changes_detected, client_address, log_type)
                
                # 更新状态缓存
                self.device_status_cache[device_sn] = current_status
                
        except Exception as e:
            print(f"[设备状态日志] 检测设备 {device_sn} 状态变化失败: {e}")
    
    def _get_change_type(self, field: str, previous_value: Any, current_value: Any) -> str:
        """
        获取状态变化类型描述
        :param field: 字段名
        :param previous_value: 之前的值
        :param current_value: 当前的值
        :return: 变化类型描述
        """
        if field.startswith('DO'):
            if current_value == 1:
                return "数字输出开启"
            elif current_value == 0:
                return "数字输出关闭"
        elif 'pump' in field:
            # 支持三档位状态：0=停止，1=手动，2=自动
            if isinstance(current_value, dict):
                mode_status = current_value.get('mode_status', current_value.get('auto_status', current_value))
                if mode_status == 0:
                    return "档位调至停止模式（强制关闭）"
                elif mode_status == 1:
                    return "档位调至手动模式（强制启动）"
                elif mode_status == 2:
                    return "档位调至自动模式（按规则运行）"
            else:
                if current_value == 0:
                    return "档位调至停止模式（强制关闭）"
                elif current_value == 1:
                    return "档位调至手动模式（强制启动）"
                elif current_value == 2:
                    return "档位调至自动模式（按规则运行）"
        
        return f"状态变化: {previous_value} -> {current_value}"
    
    def _log_status_changes(self, device_sn: str, changes: list, client_address: Optional[str] = None, log_type: str = '设备状态变化'):
        """
        记录状态变化到日志文件
        :param device_sn: 设备序列号
        :param changes: 状态变化列表
        :param client_address: 客户端地址
        """
        try:
            timestamp = datetime.datetime.now()
            
            log_entry = {
                'timestamp': timestamp.isoformat(),
                'device_sn': device_sn,
                'client_address': client_address,
                'change_count': len(changes),
                'changes': changes,
                'log_type': log_type
            }
            
            # 转换为JSON字符串
            log_message = json.dumps(log_entry, ensure_ascii=False, separators=(',', ':'))
            
            # 获取当前日志记录器并记录
            logger = self._get_current_logger()
            logger.info(log_message)
            
            # 同时在控制台输出摘要信息
            change_summary = ", ".join([f"{c['field']}: {c['change_type']}" for c in changes])
            print(f"[设备状态变化] {device_sn} - {change_summary}")
            
        except Exception as e:
            print(f"[设备状态日志] 记录设备 {device_sn} 状态变化失败: {e}")
    
    def cleanup_old_status_logs(self, keep_days: int = 30):
        """
        清理旧的设备状态变化日志文件
        :param keep_days: 保留天数
        """
        try:
            cutoff_date = datetime.date.today() - datetime.timedelta(days=keep_days)
            cutoff_date_str = cutoff_date.strftime('%Y%m%d')
            
            deleted_count = 0
            for log_file in self.logs_dir.glob("device_status_*.log"):
                try:
                    # 从文件名提取日期（格式：device_status_YYYYMMDD.log）
                    filename = log_file.stem
                    if filename.startswith('device_status_'):
                        date_part = filename.split('_')[-1]
                        if len(date_part) == 8 and date_part.isdigit():
                            if date_part < cutoff_date_str:
                                log_file.unlink()
                                deleted_count += 1
                                print(f"[设备状态日志] 删除旧日志文件: {log_file.name}")
                except (ValueError, OSError) as e:
                    print(f"[设备状态日志] 处理文件 {log_file.name} 时出错: {e}")
            
            print(f"[设备状态日志] 清理完成，删除了 {deleted_count} 个文件")
            
        except Exception as e:
            print(f"[设备状态日志] 清理旧日志文件时出错: {e}")


# 全局实例
_status_logger = None
_status_logger_lock = threading.Lock()

def get_device_status_logger() -> DeviceStatusLogger:
    """
    获取全局设备状态日志记录器实例
    :return: 设备状态日志记录器实例
    """
    global _status_logger
    if _status_logger is None:
        with _status_logger_lock:
            if _status_logger is None:
                _status_logger = DeviceStatusLogger()
    return _status_logger

def log_device_status_changes(device_sn: str, new_data: Dict[str, Any], 
                            client_address: Optional[str] = None, is_status_only: bool = False):
    """
    便捷函数：检测并记录设备状态变化
    :param device_sn: 设备序列号
    :param new_data: 设备数据或状态变化数据
    :param client_address: 客户端地址
    :param is_status_only: 是否为单独的状态变化消息
    """
    logger = get_device_status_logger()
    logger.detect_and_log_status_changes(device_sn, new_data, client_address, is_status_only)

def cleanup_old_device_status_logs(keep_days: int = 30):
    """
    便捷函数：清理旧的设备状态日志
    """
    logger = get_device_status_logger()
    logger.cleanup_old_status_logs(keep_days)


if __name__ == "__main__":
    # 测试设备状态变化检测功能
    print("测试设备状态变化检测功能...")
    
    test_device_sn = "TEST001"
    
    # 第一次数据（初始状态）
    initial_data = {
        'DO21_status': 0,
        'DO22_status': 0,
        'water_pump1': {'auto_status': 0},
        'water_pump2': {'auto_status': 0}
    }
    log_device_status_changes(test_device_sn, initial_data, "*************:8889")
    
    # 第二次数据（状态发生变化）
    changed_data = {
        'DO21_status': 1,  # 变化：0 -> 1
        'DO22_status': 0,  # 无变化
        'water_pump1': {'auto_status': 1},  # 变化：0 -> 1
        'water_pump2': {'auto_status': 0}   # 无变化
    }
    log_device_status_changes(test_device_sn, changed_data, "*************:8889")
    
    print("测试完成！")
// 高级站点API测试 - 尝试获取有效token
console.log('🔬 开始高级API测试...')

// 1. 尝试登录并获取JWT格式的token
async function advancedLoginTest() {
  console.log('\n=== 高级登录测试 ===')

  const LOGIN_URL = 'https://accountapi.usr.cn/api/Login/loginByPassword'

  try {
    const response = await fetch(LOGIN_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
        'Accept': '*/*',
        'Origin': 'https://dm.usr.cn',
        'Referer': 'https://dm.usr.cn/'
      },
      body: JSON.stringify({
        username: '***********',
        password: 'd33f11e1b1f613d4e295d77a23c04ef4',
        platformId: 'DmPub'
      })
    })

    const result = await response.json()

    if (result.status === 0) {
      console.log('✅ 登录成功，分析Token格式:')
      console.log('  原始Token:', result.data.token)

      // 检查token是否为JWT格式
      const isJWT = result.data.token.includes('.')
      console.log('  JWT格式:', isJWT ? '✅ 是' : '❌ 否')

      if (isJWT) {
        try {
          const payload = JSON.parse(atob(result.data.token.split('.')[1]))
          console.log('  JWT内容:')
          console.log('    用户ID:', payload.uid)
          console.log('    过期时间:', new Date(payload.exp * 1000).toLocaleString())
          return { success: true, token: result.data.token, isJWT: true }
        } catch (e) {
          console.log('  ⚠️ JWT解析失败:', e.message)
        }
      }

      return { success: true, token: result.data.token, isJWT: false }
    } else {
      console.log('❌ 登录失败:', result.info)
      return { success: false, error: result.info }
    }
  } catch (error) {
    console.error('❌ 登录异常:', error.message)
    return { success: false, error: error.message }
  }
}

// 2. 尝试使用不同的token格式调用站点API
async function testWithDifferentTokens(loginToken) {
  console.log('\n=== 测试不同Token格式 ===')

  // 测试用的tokens
  const tokens = [
    { name: '当前登录Token', token: loginToken },
    { name: '硬编码JWT Token', token: "eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIyMXhkOXU3MCIsInVpZCI6MjgzMTM3LCJ1c2VyTm8iOiJDTjAyMjQzNDAiLCJhdXRoIjoxLCJleHAiOjE3NTQzODA3MTcsImlhdCI6MTc1NDM3MzUxN30.Mgy06MJU0axukOXjs7Vqi9iXziwsAJi08qijOTRC0ls" }
  ]

  for (const tokenInfo of tokens) {
    console.log(`\n📋 测试 ${tokenInfo.name}:`)
    const result = await callStationAPI(tokenInfo.token)
    console.log(`  结果: ${result.success ? '✅ 成功' : '❌ 失败'}`)
    if (!result.success) {
      console.log(`  错误: ${result.error}`)
    } else {
      console.log(`  获取到 ${result.data.list.length} 个设备`)
      return result // 返回第一个成功的结果
    }
  }

  return { success: false, error: '所有token都失败' }
}

// 3. 调用站点API的核心函数
async function callStationAPI(token) {
  const API_URL = 'https://api-dm.usr.cn/dmCloud/dev/getDevs'

  const requestBody = {
    appointLoraNodeDevice: 1,
    appointSubNodeDevice: 2,
    devicesTagParamDtos: [],
    pageNo: 1,
    pageSize: 50, // 增加页面大小以获取更多设备
    projectId: "",
    searchParam: "",
    sortByWeight: "up",
    token: token
  }

  const headers = {
    'Content-Type': 'application/json',
    'Accept': '*/*',
    'Accept-Language': 'zh-CN,zh;q=0.9',
    'Origin': 'https://dm.usr.cn',
    'Referer': 'https://dm.usr.cn/',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
    'appid': 'cG8yNDYwODl6a2xqYWdpdXNkZ3E=',
    'languagetype': '0',
    'token': token,
    'traceid': '283137',
    'u-source': 'in-pc'
  }

  try {
    const response = await fetch(API_URL, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(requestBody)
    })

    const result = await response.json()

    if (result.status === 0 && result.data) {
      return { success: true, data: result.data }
    } else {
      return { success: false, error: `状态${result.status}: ${result.info}` }
    }
  } catch (error) {
    return { success: false, error: error.message }
  }
}

// 4. 分析设备数据
function analyzeDevices(devices) {
  console.log('\n📊 设备数据分析:')

  // 统计项目
  const projects = {}
  devices.forEach(device => {
    const project = device.projectName
    if (!projects[project]) {
      projects[project] = []
    }
    projects[project].push(device)
  })

  console.log('📁 项目分组:')
  Object.entries(projects).forEach(([project, devices]) => {
    console.log(`  ${project}: ${devices.length} 个设备`)
    devices.forEach(device => {
      const status = device.deviceStatus.onlineOffline === 1 ? '🟢在线' : '🔴离线'
      console.log(`    - ${device.deviceName || '未命名'} (${device.sn}) ${status}`)
    })
  })

  // 查找目标设备
  console.log('\n🎯 查找目标设备:')
  const targets = [
    { sn: '02801925060700002997', name: '联丰村' },
  ]

  targets.forEach(target => {
    const device = devices.find(d => d.sn === target.sn)
    if (device) {
      console.log(`  ✅ 找到 ${target.name} (${target.sn}):`)
      console.log(`     实际名称: ${device.deviceName || device.projectName}`)
      console.log(`     项目: ${device.projectName}`)
      console.log(`     型号: ${device.deviceModelName}`)
      console.log(`     状态: ${device.deviceStatus.onlineOffline === 1 ? '在线' : '离线'}`)
    } else {
      console.log(`  ❌ 未找到 ${target.name} (${target.sn})`)
    }
  })
}

// 5. 主测试函数
async function runAdvancedTests() {
  try {
    // 步骤1: 尝试登录获取新token
    const loginResult = await advancedLoginTest()

    if (!loginResult.success) {
      console.log('\n💥 无法获取有效token，终止测试')
      return
    }

    // 步骤2: 测试不同token调用站点API
    const apiResult = await testWithDifferentTokens(loginResult.token)

    if (apiResult.success) {
      console.log('\n🎉 成功获取站点数据!')
      console.log(`📊 总设备数: ${apiResult.data.total}`)
      console.log(`📄 当前页设备数: ${apiResult.data.list.length}`)

      // 步骤3: 分析设备数据
      analyzeDevices(apiResult.data.list)

      // 步骤4: 生成前端可用的数据
      console.log('\n💡 前端集成建议:')
      console.log('1. 更新TokenManager中的硬编码token')
      console.log('2. 或者实现自动登录逻辑')
      console.log('3. StationService.getStationList()应该可以正常工作')

    } else {
      console.log('\n😞 无法获取站点数据')
      console.log('原因:', apiResult.error)
      console.log('建议: 继续使用本地模拟数据')
    }

  } catch (error) {
    console.error('💥 测试异常:', error)
  }
}

// 运行高级测试
runAdvancedTests()

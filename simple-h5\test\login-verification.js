// 验证登录API的具体参数和响应格式
console.log('🔐 验证登录API参数和响应格式...')

async function testLoginAPIParams() {
  console.log('\n=== 测试登录API ===')
  
  const LOGIN_URL = 'https://accountapi.usr.cn/api/Login/loginByPassword'
  
  // 使用您确认的具体参数
  const requestBody = {
    password: "d33f11e1b1f613d4e295d77a23c04ef4",
    platformId: "DmPub", 
    username: "***********"
  }

  console.log('📡 请求URL:', LOGIN_URL)
  console.log('📋 请求参数:', requestBody)

  try {
    const response = await fetch(LOGIN_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': '*/*',
        'Origin': 'https://dm.usr.cn',
        'Referer': 'https://dm.usr.cn/'
      },
      body: JSON.stringify(requestBody)
    })

    console.log('📡 响应状态:', response.status)
    console.log('📡 响应头:', Object.fromEntries([...response.headers]))
    
    const result = await response.json()
    console.log('📡 完整响应数据:', JSON.stringify(result, null, 2))
    
    if (result.status === 0) {
      console.log('\n✅ 登录成功分析:')
      console.log('  状态码:', result.status)
      console.log('  消息:', result.info)
      console.log('  账户:', result.data?.account)
      console.log('  Token:', result.data?.token ? `${result.data.token.substring(0, 20)}...` : '未获取到')
      console.log('  Token长度:', result.data?.token?.length || 0)
      
      // 检查token格式
      if (result.data?.token) {
        const token = result.data.token
        const isJWT = token.includes('.')
        console.log('  Token格式:', isJWT ? 'JWT格式' : '普通字符串格式')
        
        if (isJWT) {
          try {
            const payload = JSON.parse(atob(token.split('.')[1]))
            console.log('  JWT内容:', payload)
          } catch (e) {
            console.log('  JWT解析失败:', e.message)
          }
        }
      }
      
      return { success: true, data: result.data }
    } else {
      console.log('\n❌ 登录失败:')
      console.log('  状态码:', result.status)
      console.log('  错误信息:', result.info)
      return { success: false, error: result.info }
    }
  } catch (error) {
    console.error('❌ 请求异常:', error.message)
    return { success: false, error: error.message }
  }
}

// 如果登录成功，测试使用获取到的token调用站点API
async function testWithLoginToken(token) {
  console.log('\n=== 使用登录token测试站点API ===')
  console.log('🔑 使用Token:', token.substring(0, 20) + '...')
  
  const API_URL = 'https://api-dm.usr.cn/dmCloud/dev/getDevs'
  
  const requestBody = {
    appointLoraNodeDevice: 1,
    appointSubNodeDevice: 2,
    devicesTagParamDtos: [],
    pageNo: 1,
    pageSize: 10,
    projectId: "",
    searchParam: "",
    sortByWeight: "up",
    token: token
  }

  const headers = {
    'Content-Type': 'application/json',
    'Accept': '*/*',
    'Accept-Language': 'zh-CN,zh;q=0.9',
    'Origin': 'https://dm.usr.cn',
    'Referer': 'https://dm.usr.cn/',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'appid': 'cG8yNDYwODl6a2xqYWdpdXNkZ3E=',
    'languagetype': '0',
    'token': token,
    'traceid': '283137',
    'u-source': 'in-pc'
  }

  try {
    const response = await fetch(API_URL, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(requestBody)
    })

    const result = await response.json()
    console.log('📡 站点API响应:', JSON.stringify(result, null, 2))
    
    if (result.status === 0 && result.data) {
      console.log('✅ 站点API调用成功!')
      console.log(`📊 获取到 ${result.data.list.length} 个站点`)
      return { success: true, data: result.data }
    } else {
      console.log('❌ 站点API调用失败:')
      console.log('  状态码:', result.status)
      console.log('  错误信息:', result.info)
      return { success: false, error: result.info, status: result.status }
    }
  } catch (error) {
    console.error('❌ 站点API异常:', error.message)
    return { success: false, error: error.message }
  }
}

// 主测试函数
async function runLoginVerification() {
  try {
    console.log('开始验证登录API...')
    
    // 1. 测试登录API
    const loginResult = await testLoginAPIParams()
    
    if (!loginResult.success) {
      console.log('\n💥 登录失败，无法继续测试')
      return
    }
    
    // 2. 如果登录成功，测试站点API
    if (loginResult.data.token) {
      const stationResult = await testWithLoginToken(loginResult.data.token)
      
      // 3. 总结结果
      console.log('\n🎯 完整测试总结:')
      console.log('  登录API: ✅ 成功')
      console.log('  站点API:', stationResult.success ? '✅ 成功' : '❌ 失败')
      
      if (stationResult.success) {
        console.log('\n🎉 完美! 登录->站点数据获取 完整流程成功!')
        console.log('💡 这意味着我们的TokenManager逻辑是正确的')
      } else {
        console.log('\n⚠️ 登录token无法直接用于站点API')
        console.log('可能需要JWT格式的token')
        console.log('错误详情:', stationResult.error)
      }
    } else {
      console.log('\n❌ 登录成功但未获取到token')
    }
    
  } catch (error) {
    console.error('💥 测试异常:', error)
  }
}

// 运行验证
runLoginVerification()
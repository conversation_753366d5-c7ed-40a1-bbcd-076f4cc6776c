// 实际Vue应用API测试组件
// 可以添加到StationSelectView.vue中用于调试

async function debugStationAPI() {
  console.log('🧪 Vue应用内API测试开始...')
  
  try {
    // 直接导入并测试StationService
    const { StationService } = await import('@/services/stationService')
    
    console.log('📦 StationService导入成功')
    
    // 测试API调用
    const stations = await StationService.getStationList()
    
    console.log('✅ API调用成功!')
    console.log('获取到站点数量:', stations.length)
    console.log('站点详细信息:', stations)
    
    // 分析获取到的数据
    stations.forEach((station, index) => {
      console.log(`${index + 1}. ${station.deviceName || station.projectName}`)
      console.log(`   SN: ${station.sn}`)
      console.log(`   项目: ${station.projectName}`)
      console.log(`   在线: ${station.deviceStatus.onlineOffline === 1 ? '是' : '否'}`)
    })
    
    return stations
    
  } catch (error) {
    console.error('❌ API测试失败:', error)
    console.error('错误详情:', error.message)
    return null
  }
}

// 自动执行测试
debugStationAPI()
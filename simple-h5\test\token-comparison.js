// 分析本地测试和部署后token的差异
console.log('🔍 分析本地测试 vs 部署后的token差异...')

function analyzeToken(token, name) {
  console.log(`\n=== ${name} ===`)
  console.log('Token:', token)
  
  try {
    const payload = JSON.parse(atob(token.split('.')[1]))
    console.log('解码内容:', payload)
    
    const issuedAt = new Date(payload.iat * 1000)
    const expiresAt = new Date(payload.exp * 1000)
    const currentTime = new Date()
    
    console.log('签发时间:', issuedAt.toLocaleString())
    console.log('过期时间:', expiresAt.toLocaleString())
    console.log('当前时间:', currentTime.toLocaleString())
    
    const isValid = payload.exp > Math.floor(Date.now() / 1000)
    console.log('状态:', isValid ? '🟢 有效' : '🔴 已过期')
    
    if (!isValid) {
      const hoursExpired = Math.floor((Date.now() - payload.exp * 1000) / (1000 * 60 * 60))
      console.log('过期时长:', hoursExpired, '小时前')
    }
    
    return { valid: isValid, payload }
  } catch (error) {
    console.error('解码失败:', error.message)
    return { valid: false, error: error.message }
  }
}

// 本地测试token
const localToken = "eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIyMXhkOXU3MCIsInVpZCI6MjgzMTM3LCJ1c2VyTm8iOiJDTjAyMjQzNDAiLCJhdXRoIjoxLCJleHAiOjE3NTQ0NzU5NDUsImlhdCI6MTc1NDQ2ODc0NX0.xfFfFXAZON1NTbM2P0Hb6pzYNcFL63RuC2ljy4HTSJQ"

// 部署后token  
const deployedToken = "eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIyMXhkOXU3MCIsInVpZCI6MjgzMTM3LCJ1c2VyTm8iOiJDTjAyMjQzNDAiLCJhdXRoIjoxLCJleHAiOjE3NTQzODA3MTcsImlhdCI6MTc1NDM3MzUxN30.Mgy06MJU0axukOXjs7Vqi9iXziwsAJi08qijOTRC0ls"

console.log('🎯 问题分析:')
console.log('本地测试能成功 - 使用的是新token')
console.log('部署后失败 - 使用的是旧token')
console.log('错误信息: "Current session timeout,please login again"')

const localResult = analyzeToken(localToken, '本地测试Token')
const deployedResult = analyzeToken(deployedToken, '部署后Token')

console.log('\n🔧 解决方案:')
if (localResult.valid && !deployedResult.valid) {
  console.log('✅ 确认：本地token有效，部署token已过期')
  console.log('💡 需要更新部署环境中的token为本地测试使用的有效token')
  console.log('📝 具体操作:')
  console.log('   1. 更新TokenManager中的硬编码token')
  console.log('   2. 重新构建和部署应用')
  console.log('   3. 或者实现动态token获取机制')
} else {
  console.log('⚠️ 需要进一步分析token状态')
}
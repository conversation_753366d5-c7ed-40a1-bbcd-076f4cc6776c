# Node-RED 快速入门指南

## 5分钟快速上手

### 第一步：安装和启动
```bash
# 安装 Node-RED
npm install -g node-red

# 启动 Node-RED
node-red
```

打开浏览器访问：`http://localhost:1880`

### 第二步：创建第一个流程

#### 示例1：简单的消息处理
1. 从左侧面板拖拽以下节点到画布：
   - **Inject** 节点（输入）
   - **Function** 节点（处理）
   - **Debug** 节点（输出）

2. 连接节点：Inject → Function → Debug

3. 配置 Function 节点：
```javascript
// 双击 Function 节点，在代码框中输入：
msg.payload = "Hello, " + msg.payload + "!";
return msg;
```

4. 点击右上角的红色"部署"按钮

5. 点击 Inject 节点左侧的按钮测试流程

#### 示例2：HTTP API 创建
1. 拖拽节点：
   - **HTTP in** 节点
   - **Function** 节点
   - **HTTP response** 节点

2. 连接：HTTP in → Function → HTTP response

3. 配置 HTTP in 节点：
   - Method: GET
   - URL: /hello

4. 配置 Function 节点：
```javascript
msg.payload = {
    message: "Hello World",
    timestamp: new Date().toISOString()
};
return msg;
```

5. 部署后访问：`http://localhost:1880/hello`

## 常用节点速查

### 输入节点
| 节点 | 功能 | 常用配置 |
|------|------|----------|
| Inject | 手动触发 | 按钮、定时器 |
| HTTP in | 接收HTTP请求 | 路径、方法 |
| MQTT in | 接收MQTT消息 | 主题、代理 |
| WebSocket in | WebSocket连接 | 路径 |

### 处理节点
| 节点 | 功能 | 常用配置 |
|------|------|----------|
| Function | JavaScript代码 | 自定义逻辑 |
| Switch | 条件分支 | 多个条件 |
| Change | 修改消息 | 设置/删除属性 |
| Template | 文本模板 | Mustache语法 |

### 输出节点
| 节点 | 功能 | 常用配置 |
|------|------|----------|
| Debug | 调试输出 | 控制台显示 |
| HTTP response | HTTP响应 | 状态码、头部 |
| MQTT out | 发送MQTT消息 | 主题、代理 |
| File | 文件操作 | 读写文件 |

## 实用示例

### 1. 数据转换
```javascript
// Function 节点示例
var data = msg.payload;
msg.payload = {
    id: data.id,
    name: data.name.toUpperCase(),
    value: parseFloat(data.value) * 2
};
return msg;
```

### 2. 条件判断
```javascript
// Switch 节点配置
// 条件1: msg.payload.temperature > 30
// 条件2: msg.payload.temperature < 10
// 默认: 其他情况
```

### 3. 错误处理
```javascript
// Function 节点中的错误处理
try {
    var result = JSON.parse(msg.payload);
    msg.payload = result;
} catch (error) {
    msg.error = error.message;
    node.error("JSON解析失败", msg);
}
return msg;
```

## 调试技巧

### 1. 使用 Debug 节点
- 拖拽 Debug 节点到流程中
- 配置要输出的属性（如 `msg.payload`）
- 在右侧调试面板查看输出

### 2. 状态监控
- 使用 Status 节点监控节点状态
- 在节点上右键选择"显示状态"

### 3. 手动测试
- 使用 Inject 节点手动触发流程
- 配置测试数据

## 常见问题解决

### 问题1：节点显示红色
**原因**：配置错误或连接失败
**解决**：检查节点配置，查看错误信息

### 问题2：流程不执行
**原因**：连线断开或节点未部署
**解决**：重新连接连线，点击部署按钮

### 问题3：数据格式错误
**原因**：消息格式不匹配
**解决**：使用 Debug 节点检查数据格式

## 下一步学习

1. **阅读完整文档**：查看 `Node-RED完整使用指南.md`
2. **尝试复杂流程**：结合多个节点创建复杂应用
3. **学习自定义节点**：开发特定功能的节点
4. **参与社区**：访问 Node-RED 官方社区

## 快捷键

| 快捷键 | 功能 |
|--------|------|
| Ctrl+S | 保存流程 |
| Ctrl+Z | 撤销 |
| Ctrl+Y | 重做 |
| Delete | 删除选中节点 |
| Ctrl+A | 全选 |

## 小贴士

- 定期保存和备份流程
- 使用有意义的节点名称
- 添加注释说明流程功能
- 测试每个节点配置
- 使用子流程组织复杂流程

---

*更多详细信息请参考完整使用指南文档。* 
<template>
  <div class="container">
    <!-- 顶部信息区 -->
    <div class="top-info-section">
      <div class="station-header">
        <div class="station-title">
          <h1>{{ currentStation?.name || '水利智能管理平台' }}</h1>
          <div class="station-meta">
            <span class="station-status" :class="getSystemStatusClass()">{{ getSystemStatus() }}</span>
            <span class="current-time">{{ currentTime }}</span>
          </div>
        </div>
        <div class="header-actions">
          <button @click="goBack" class="header-button back-button">返回站点</button>
          <router-link :to="historyRoute" class="header-button">查看历史数据</router-link>
        </div>
      </div>

      <!-- 集成浮球状态和温湿度信息 -->
      <div class="integrated-info">
        <div class="info-row">
          <span class="info-label">浮球状态:</span>
          <span class="info-value" :class="getFloatStatusClass()">{{ getFloatStatusText() }}</span>
        </div>
        <div class="info-row">
          <span class="info-label">实时温度:</span>
          <span class="info-value">{{ getTemperatureDisplay() }}</span>
          <span class="info-label">实时湿度:</span>
          <span class="info-value">{{ getHumidityDisplay() }}</span>
        </div>
      </div>
    </div>

    <!-- 设备运行状态区 -->
    <div class="device-status-section">
      <h2 class="section-title">设备运行状态</h2>
      <div class="device-grid">
        <div v-for="device in deviceStatusList" :key="device.id" class="device-compact-card"
          :class="getDeviceTypeClass(device.name)">
          <div class="device-header-compact">
            <div class="device-name-section">
              <span class="device-name-compact">{{ device.name }}</span>
              <span class="device-voltage">{{ getDeviceVoltage(device.name) }}</span>
            </div>
            <span class="device-current-compact">{{ device.current }}</span>
          </div>
          <div class="device-status-compact">
            <span class="status-item">
              <span class="status-value" :class="device.runStatus.class">{{ device.runStatus.text }}</span>
            </span>
            <span class="status-item">
              <span class="status-label">模式:</span>
              <span class="status-value" :class="device.gearStatus.class">{{ device.gearStatus.text }}</span>
            </span>
          </div>
          <div class="device-check-compact">
            截止{{ device.lastCheckTime }}已自检
            <span class="self-check-result" :class="device.selfCheck.class">{{ device.selfCheck.text }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 能耗数据看板区 -->
    <div class="energy-dashboard-section">
      <h2 class="section-title">能耗数据看板</h2>
      <div class="energy-grid">
        <div v-for="energy in energyData" :key="energy.label" class="energy-card">
          <div class="energy-label">{{ energy.label }}</div>
          <div class="energy-value">{{ energy.value }}</div>
          <div class="energy-unit">{{ energy.unit }}</div>
        </div>
      </div>
    </div>

    <!-- 任务调度区 -->
    <div class="task-schedule-section">
      <h2 class="section-title">任务调度</h2>
      <button class="new-task-btn" @click="showTaskCreation = !showTaskCreation">
        {{ showTaskCreation ? '收起' : '新建任务' }}
      </button>

      <!-- 任务状态摘要 -->
      <div class="task-summary">
        <div class="task-summary-item">
          <div class="task-type-label">单次任务</div>
          <div class="task-summary-info">
            {{ getSingleTaskSummary() }}
          </div>
        </div>

        <div class="task-summary-item">
          <div class="task-type-label">循环任务</div>
          <div class="task-summary-info">
            {{ getCycleTaskSummary() }}
          </div>
        </div>

        <div class="task-summary-item">
          <div class="task-type-label">顺序任务</div>
          <div class="task-summary-info">
            {{ getSequenceTaskSummary() }}
          </div>
        </div>
      </div>

      <!-- 正在进行中的任务 -->
      <div class="running-tasks" v-if="hasRunningTasks">
        <h3 class="subsection-title">
          正在进行中的任务
          <span class="task-count-badge">{{ activeTasksData?.active_tasks_count || 0 }}</span>
        </h3>
        <div class="active-task-list">
          <div v-for="task in getActiveTasksDisplay()" :key="task.id" class="active-task-item"
            :class="{ 'task-unhealthy': task.isHealthy === false }">
            <div class="task-main-info">
              <div class="task-header">
                <div class="task-device">{{ task.deviceName }}</div>
                <div class="task-type-badge" :class="getTaskTypeClass(task.type)">
                  {{ task.type }}
                </div>
              </div>
              <div class="task-status-text">{{ task.statusText }}</div>
              <div class="task-details">
                <div class="task-remaining" v-if="task.remainingTime">
                  剩余时间: {{ task.remainingTime }}
                </div>
                <div class="task-next-action" v-if="task.nextAction && task.nextAction !== '未知'">
                  下一步: {{ task.nextAction }}
                </div>
                <div class="task-health" v-if="task.isHealthy === false">
                  <span class="health-warning">⚠️ 任务异常</span>
                </div>
              </div>
            </div>
            <div class="task-action-buttons">
              <button class="cancel-slide-btn" @click="cancelTask(task.id, task.type)">
                取消任务
              </button>
              <button class="modify-task-btn" @click="modifyTask(task.id)" disabled>
                修改任务
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 原有功能区域 (收起状态) -->
    <div class="advanced-controls-section">
      <div class="section-header" @click="showAdvancedControls = !showAdvancedControls">
        <h2 class="section-title">高级控制功能</h2>
        <span class="toggle-icon" :class="{ 'expanded': showAdvancedControls }">▼</span>
      </div>

      <div v-show="showAdvancedControls" class="advanced-controls-content">
        <div class="card">
          <div class="card-header">
            <div>
              <span>设备控制</span>
              <div class="station-info" v-if="currentStation">
                <small>{{ currentStation.name }} ({{ currentStation.sn }})</small>
              </div>
            </div>
          </div>
          <div class="card-body">
            <div v-for="device in deviceControls" :key="device.id" class="device-control-item">
              <div class="device-header">
                <span class="device-label">{{ device.label }}</span>
                <span class="device-current">
                  实时电流: <strong>{{ device.current?.toFixed(3) ?? '--' }} A</strong>
                </span>
              </div>
              <div class="knob-switch">
                <div class="knob-switch-option" :class="{ active: device.status === 'manual' }" data-state="manual">
                  手动
                </div>
                <div class="knob-switch-option" :class="{ active: device.status === 'stop' }" data-state="stop">
                  停止
                </div>
                <div class="knob-switch-option" :class="{ active: device.status === 'auto' }" data-state="auto">
                  自动
                </div>
              </div>
            </div>
            <p class="non-clickable-note">
              注：以上为设备状态显示，如需操作请使用"DO直控"或"定时任务"功能。
            </p>
          </div>
        </div>

        <!-- 浮球状态 -->
        <div class="card">
          <div class="card-header">浮球状态</div>
          <div class="card-body">
            <div class="info-grid">
              <div v-for="item in floatSwitchStatus" :key="item.label" class="info-item">
                <div class="label">{{ item.label }}</div>
                <div class="value">
                  {{ item.value }}<span v-if="item.unit" class="unit">{{ item.unit }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 温湿度 -->
        <div class="card">
          <div class="card-header">温湿度</div>
          <div class="card-body">
            <div class="info-grid">
              <div v-for="item in temperatureHumidityStatus" :key="item.label" class="info-item">
                <div class="label">{{ item.label }}</div>
                <div class="value">
                  {{ item.value }}<span v-if="item.unit" class="unit">{{ item.unit }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 电能表 -->
        <div class="card">
          <div class="card-header">电能表</div>
          <div class="card-body">
            <ul class="power-details-list">
              <li v-for="item in powerMeterDetails" :key="item.label">
                <span class="label">{{ item.label }}</span>
                <span class="value">{{ item.value ?? '--' }} {{ item.unit }}</span>
              </li>
            </ul>
          </div>
        </div>

        <!-- DO 直控 -->
        <div class="card">
          <div class="card-header">DO 直控</div>
          <div class="card-body">
            <ul class="do-control-list">
              <li v-for="doItem in doControls" :key="doItem.name">
                <span class="label">{{ doItem.label }}</span>
                <div class="actions">
                  <button @click="controlDo(doItem.name, 1)" class="btn btn-on">开启</button>
                  <button @click="controlDo(doItem.name, 0)" class="btn btn-off">关闭</button>
                </div>
              </li>
            </ul>
          </div>
        </div>

        <!-- 定时任务 -->
        <div class="card" v-show="showTaskCreation">
          <div class="card-header">定时任务</div>
          <div class="card-body">
            <!-- 任务类型切换 -->
            <div class="task-type-switcher">
              <button :class="{ active: taskType === 'single' }" @click="taskType = 'single'">
                单次任务
              </button>
              <button :class="{ active: taskType === 'cycle' }" @click="taskType = 'cycle'">
                循环任务
              </button>
              <button :class="{ active: taskType === 'sequence' }" @click="taskType = 'sequence'">
                顺序任务
              </button>
            </div>

            <!-- 单次任务 -->
            <div v-if="taskType === 'single'">
              <!-- 创建任务表单 -->
              <div class="task-scheduler-form">
                <h3 class="form-title">创建新任务</h3>
                <div class="form-group">
                  <label for="do-select">选择DO:</label>
                  <select id="do-select" v-model="newTask.do_name">
                    <option v-for="doItem in doControls" :key="doItem.name" :value="doItem.name">
                      {{ doItem.label }}
                    </option>
                  </select>
                </div>
                <div class="form-group">
                  <label for="action-select">选择操作:</label>
                  <select id="action-select" v-model.number="newTask.value">
                    <option :value="1">开启</option>
                    <option :value="0">关闭</option>
                  </select>
                </div>
                <div class="form-group">
                  <label for="delay-input">延迟时间 (分钟):</label>
                  <input id="delay-input" type="number" v-model.number="newTask.delay_minutes" min="1"
                    placeholder="输入分钟数" />
                </div>
                <button @click="scheduleNewTask" class="btn-submit" :disabled="!isSingleTaskFormValid.valid">
                  添加任务
                </button>
                <p v-if="!isSingleTaskFormValid.valid" class="form-error-message">
                  {{ isSingleTaskFormValid.message }}
                </p>
              </div>

              <!-- 任务列表 -->
              <div class="scheduled-tasks-list">
                <h3 class="list-title">已安排的单次任务</h3>
                <ul v-if="Object.keys(scheduledTasks).length > 0">
                  <li v-for="(task, jobId) in scheduledTasks" :key="jobId">
                    <span>
                      <strong>{{ getDoLabel(task.args[1]) }}</strong> 将在
                      {{ new Date(task.run_date).toLocaleString() }}
                      <strong>{{ task.args[2] === 1 ? '开启' : '关闭' }}</strong>
                    </span>
                    <button @click="cancelScheduledTask(jobId)" class="btn-cancel">取消</button>
                  </li>
                </ul>
                <p v-else>当前没有已安排的单次任务。</p>
              </div>
            </div>

            <!-- 循环任务 -->
            <div v-if="taskType === 'cycle'">
              <!-- 创建循环任务表单 -->
              <div class="task-scheduler-form">
                <h3 class="form-title">创建循环任务</h3>
                <div class="form-group">
                  <label for="cycle-do-select">选择DO:</label>
                  <select id="cycle-do-select" v-model="newCycleTask.do_name">
                    <option v-for="doItem in doControls" :key="doItem.name" :value="doItem.name">
                      {{ doItem.label }}
                    </option>
                  </select>
                </div>
                <div class="form-group">
                  <label for="on-minutes-input">开启时长 (分钟):</label>
                  <input id="on-minutes-input" type="number" v-model.number="newCycleTask.on_minutes" min="1"
                    placeholder="例如: 240" />
                </div>
                <div class="form-group">
                  <label for="off-minutes-input">关闭时长 (分钟):</label>
                  <input id="off-minutes-input" type="number" v-model.number="newCycleTask.off_minutes" min="1"
                    placeholder="例如: 120" />
                </div>
                <button @click="scheduleNewCycleTask" class="btn-submit" :disabled="!isCycleTaskFormValid.valid">
                  启动循环任务
                </button>
                <p v-if="!isCycleTaskFormValid.valid" class="form-error-message">
                  {{ isCycleTaskFormValid.message }}
                </p>
              </div>

              <!-- 循环任务列表 -->
              <div class="scheduled-tasks-list">
                <h3 class="list-title">活动的循环任务</h3>
                <ul v-if="Object.keys(cyclicTasks).length > 0">
                  <li v-for="(task, cycleId) in cyclicTasks" :key="cycleId">
                    <span>
                      <strong>{{ getDoLabel(task.do_name) }}</strong>: 开启
                      <strong>{{ task.on_minutes }}</strong> 分钟, 关闭
                      <strong>{{ task.off_minutes }}</strong> 分钟
                    </span>
                    <button @click="cancelCyclicTask(cycleId)" class="btn-cancel">取消循环</button>
                  </li>
                </ul>
                <p v-else>当前没有活动的循环任务。</p>
              </div>
            </div>

            <!-- 顺序任务 -->
            <div v-if="taskType === 'sequence'">
              <div class="task-scheduler-form">
                <h3 class="form-title">创建顺序循环任务</h3>
                <p class="form-description">A运行指定分钟后关闭，并立即启动B，B运行完后重新启动A，无限循环。</p>

                <!-- 设备A -->
                <div class="form-group">
                  <label for="sequence-do-a-select">设备A:</label>
                  <select id="sequence-do-a-select" v-model="newSequenceTask.do_a_name">
                    <option v-for="doItem in doControls" :key="doItem.name" :value="doItem.name">
                      {{ doItem.label }}
                    </option>
                  </select>
                </div>
                <div class="form-group">
                  <label for="sequence-a-minutes-input">设备A运行时长 (分钟):</label>
                  <input id="sequence-a-minutes-input" type="number" v-model.number="newSequenceTask.do_a_minutes"
                    min="1" />
                </div>

                <!-- 设备B -->
                <div class="form-group">
                  <label for="sequence-do-b-select">设备B:</label>
                  <select id="sequence-do-b-select" v-model="newSequenceTask.do_b_name">
                    <option v-for="doItem in doControls" :key="doItem.name" :value="doItem.name">
                      {{ doItem.label }}
                    </option>
                  </select>
                </div>
                <div class="form-group">
                  <label for="sequence-b-minutes-input">设备B运行时长 (分钟):</label>
                  <input id="sequence-b-minutes-input" type="number" v-model.number="newSequenceTask.do_b_minutes"
                    min="1" />
                </div>

                <button @click="scheduleNewSequenceTask" class="btn-submit" :disabled="!isSequenceTaskFormValid.valid">
                  启动顺序任务
                </button>
                <p v-if="!isSequenceTaskFormValid.valid" class="form-error-message">
                  {{ isSequenceTaskFormValid.message }}
                </p>
              </div>

              <!-- 顺序任务列表 -->
              <div class="scheduled-tasks-list">
                <h3 class="list-title">活动的顺序任务</h3>
                <ul v-if="Object.keys(sequenceTasks).length > 0">
                  <li v-for="(task, sequenceId) in sequenceTasks" :key="sequenceId">
                    <span>
                      <strong>{{ getDoLabel(task.do_a_name) }}</strong> ({{ task.do_a_minutes }}分) →
                      <strong>{{ getDoLabel(task.do_b_name) }}</strong> ({{ task.do_b_minutes }}分)
                    </span>
                    <button @click="cancelSequenceTask(sequenceId)" class="btn-cancel">取消</button>
                  </li>
                </ul>
                <p v-else>当前没有活动的顺序任务。</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 设备信息 -->
        <div class="card">
          <div class="card-header">设备信息</div>
          <div class="card-body">
            <ul class="device-info-list">
              <li v-for="item in deviceInfo" :key="item.label">
                <span class="label">{{ item.label }}</span>
                <span class="value">{{ item.value ?? '--' }}</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, reactive } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

// 接收路由参数
const props = defineProps<{
  stationId: string
}>()

// 新增响应式变量
const showAdvancedControls = ref(false)
const showTaskCreation = ref(false)
const currentTime = ref(new Date().toLocaleString())

// 更新当前时间
onMounted(() => {
  const updateTime = () => {
    currentTime.value = new Date().toLocaleString()
  }
  setInterval(updateTime, 1000)
})

// 站点信息接口
interface StationInfo {
  id: string
  name: string
  sn: string
  baseUrl: string
}

// 当前站点信息
const currentStation = ref<StationInfo | null>(null)

// 从路由参数获取站点信息
function getCurrentStationFromRoute(): StationInfo | null {
  const { sn, stationName } = route.query

  if (sn && stationName) {
    return {
      id: props.stationId,
      name: stationName as string,
      sn: sn as string,
      baseUrl: 'http://49.235.191.145:8500' // 使用现有的后端服务地址
    }
  }
  return null
}

// 返回站点选择页面
function goBack() {
  router.push('/')
}

// 历史数据路由
const historyRoute = computed(() => ({
  name: 'history',
  params: { stationId: props.stationId },
  query: route.query
}))

// -- 新增设备映射 (与后端保持一致) --
const DO_TO_DEVICE_MAPPING: Record<string, keyof DeviceData> = {
  DO21: 'water_pump1',
  DO22: 'water_pump2',
  DO23: 'air_pump1',
  DO24: 'air_pump2'
}
// -- 映射结束 --

// Define interfaces for our data structures to avoid using 'any'
interface DeviceStatus {
  auto_status: number;
  stop_status: number;
  manual_status: number;
}

interface DeviceControlItem {
  id: string;
  label: string;
  status: 'auto' | 'stop' | 'manual' | null;
  current?: number;
}



// 从API返回的完整设备数据结构
interface DeviceData {
  float_switches: { float1: number };
  water_pump1: DeviceStatus;
  water_pump2: DeviceStatus;
  air_pump1: DeviceStatus;
  air_pump2: DeviceStatus;
  device_info: {
    sn: string;
    fw_version: string;
    imei: string;
  };
  last_updated: string;
  dianliucaiji2: {
    curr1_ch1?: number;
    curr1_ch2?: number;
    curr1_ch3?: number;
    curr1_ch4?: number;
    curr2_ch1?: number;
    curr2_ch2?: number;
    curr2_ch3?: number;
    curr2_ch4?: number;
  };
  wenshidu: {
    humidity: number;
    temperature: number;
  };
  diannengbiao: {
    voltages: { Ua: number; Ub: number; Uc: number };
    currents: { Ia: number; Ib: number; Ic: number };
    active_power: { total: number };
    reactive_power: { total: number };
    active_energy?: number;
    reverse_active_energy?: number;
  };
  timestamp: string;
  // DO状态指示
  DO21_status?: number;
  DO22_status?: number;
  DO23_status?: number;
  DO24_status?: number;
}

// 定义常量 - 从路由参数动态获取
const SN = computed(() => currentStation.value?.sn || '02801925060700002997') // 默认设备SN
const BASE_URL = computed(() => currentStation.value?.baseUrl || 'http://49.235.191.145:8500') // 默认API基础地址

// 使用 ref 创建响应式状态
const deviceData = ref<DeviceData | null>(null)

// DO控制和任务调度相关状态
const doControls = ref([
  { name: 'DO21', label: '水泵1' },
  { name: 'DO22', label: '水泵2' },
  { name: 'DO23', label: '气泵1' },
  { name: 'DO24', label: '气泵2' }
])
const scheduledTasks = ref<Record<string, { run_date: string; args: [string, string, number] }>>({})
const newTask = reactive({
  do_name: 'DO21',
  value: 1, // 1 for ON, 0 for OFF
  delay_minutes: 60
})
// 新增：循环任务相关状态
const taskType = ref('single') // 'single' or 'cycle' or 'sequence'
const cyclicTasks = ref<
  Record<string, { do_name: string; on_minutes: number; off_minutes: number }>
>({})
const newCycleTask = reactive({
  do_name: 'DO21',
  on_minutes: 240,
  off_minutes: 120
})
// 新增：顺序任务相关状态
const sequenceTasks = ref<
  Record<
    string,
    { do_a_name: string; do_a_minutes: number; do_b_name: string; do_b_minutes: number }
  >
>({})
const newSequenceTask = reactive({
  do_a_name: 'DO23', // 默认为气泵1
  do_a_minutes: 30,
  do_b_name: 'DO24', // 默认为气泵2
  do_b_minutes: 30
})

// 新增：活动任务数据
interface ActiveTaskItem {
  id: string;
  type: 'cycle' | 'sequence';
  device_name: string;
  status_text: string;
  remaining_time: string;
  next_action: string;
  device_sn: string;
  cycle_info?: {
    on_minutes: number;
    off_minutes: number;
  };
  sequence_info?: {
    device_a: string;
    device_b: string;
    do_a_minutes: number;
    do_b_minutes: number;
    healthy: boolean;
  };
}

interface ActiveTasksResponse {
  success: boolean;
  timestamp: string;
  active_tasks_count: number;
  active_tasks: ActiveTaskItem[];
}

const activeTasksData = ref<ActiveTasksResponse | null>(null)  // 存储从/schedule/tasks/active获取的完整数据

// -- 新增：表单验证计算属性 --
const isSingleTaskFormValid = computed(() => {
  const deviceKey = DO_TO_DEVICE_MAPPING[newTask.do_name]
  if (!deviceData.value || !deviceKey) {
    return { valid: false, message: '设备数据加载中...' }
  }
  const module = deviceData.value[deviceKey] as DeviceStatus | undefined
  if (module?.auto_status !== 1) {
    return { valid: false, message: '所选设备必须处于自动模式才能安排任务。' }
  }
  return { valid: true, message: '' }
})

const isCycleTaskFormValid = computed(() => {
  const deviceKey = DO_TO_DEVICE_MAPPING[newCycleTask.do_name]
  if (!deviceData.value || !deviceKey) {
    return { valid: false, message: '设备数据加载中...' }
  }
  const module = deviceData.value[deviceKey] as DeviceStatus | undefined
  if (module?.auto_status !== 1) {
    return { valid: false, message: '所选设备必须处于自动模式才能安排任务。' }
  }
  return { valid: true, message: '' }
})

const isSequenceTaskFormValid = computed(() => {
  const deviceAKey = DO_TO_DEVICE_MAPPING[newSequenceTask.do_a_name]
  const deviceBKey = DO_TO_DEVICE_MAPPING[newSequenceTask.do_b_name]

  if (newSequenceTask.do_a_name === newSequenceTask.do_b_name) {
    return { valid: false, message: '设备A和设备B不能是同一个设备。' }
  }

  if (!deviceData.value || !deviceAKey || !deviceBKey) {
    return { valid: false, message: '设备数据加载中...' }
  }

  const moduleA = deviceData.value[deviceAKey] as DeviceStatus | undefined
  const moduleB = deviceData.value[deviceBKey] as DeviceStatus | undefined

  if (moduleA?.auto_status !== 1 || moduleB?.auto_status !== 1) {
    return { valid: false, message: '两个所选设备都必须处于自动模式才能安排任务。' }
  }

  return { valid: true, message: '' }
})
// -- 验证计算属性结束 --

// onMounted 生命周期钩子中调用接口获取数据
onMounted(() => {
  // 获取当前站点信息
  currentStation.value = getCurrentStationFromRoute()

  fetchDeviceData()
  fetchPowerConsumptionStats()
  // 同时获取所有任务类型
  fetchScheduledTasks()
  fetchCyclicTasks()
  fetchSequenceTasks()
  fetchActiveTasksData()

  // 定时刷新设备数据和任务数据
  setInterval(() => {
    fetchDeviceData()
    fetchPowerConsumptionStats()
    fetchScheduledTasks()
    fetchCyclicTasks()
    fetchSequenceTasks()
    fetchActiveTasksData()
  }, 30000)
})

/**
 * 从API获取设备数据
 */
async function fetchDeviceData() {
  const apiUrl = `${BASE_URL.value}/data/${SN.value}`

  try {
    const response = await fetch(apiUrl)
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    // 直接将获取到的数据赋值给deviceData
    deviceData.value = await response.json()
  } catch (error) {
    console.error('获取设备数据失败:', error)
  }
}

/**
 * 控制DO开关
 * @param doName - DO的名称, e.g., 'DO21'
 * @param value - 状态, 1 for ON, 0 for OFF
 */
async function controlDo(doName: string, value: number) {
  const apiUrl = `${BASE_URL.value}/control/${SN.value}`
  try {
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ do_name: doName, value: value })
    })
    const result = await response.json()
    if (!response.ok) {
      throw new Error(result.detail || `HTTP error! status: ${response.status}`)
    }
    alert(result.message) // Simple feedback
  } catch (error) {
    console.error(`控制 ${doName} 失败:`, error)
    alert(`控制 ${doName} 失败: ${error}`)
  }
}

/**
 * 获取活动任务状态
 */
async function fetchActiveTasksData() {
  const apiUrl = `${BASE_URL.value}/schedule/tasks/active`
  try {
    const response = await fetch(apiUrl)
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    const result: ActiveTasksResponse = await response.json()
    activeTasksData.value = result
  } catch (error) {
    console.error('获取活动任务失败:', error)
    activeTasksData.value = null
  }
}

/**
 * 获取计划任务列表
 */
async function fetchScheduledTasks() {
  const apiUrl = `${BASE_URL.value}/schedule/tasks`
  try {
    const response = await fetch(apiUrl)
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    scheduledTasks.value = await response.json()
  } catch (error) {
    console.error('获取计划任务失败:', error)
  }
}

/**
 * 获取循环任务列表
 */
async function fetchCyclicTasks() {
  const apiUrl = `${BASE_URL.value}/schedule/cycles`
  try {
    const response = await fetch(apiUrl)
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    cyclicTasks.value = await response.json()
  } catch (error) {
    console.error('获取循环任务失败:', error)
  }
}

/**
 * 获取顺序任务列表
 */
async function fetchSequenceTasks() {
  const apiUrl = `${BASE_URL.value}/schedule/sequences`
  try {
    const response = await fetch(apiUrl)
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    sequenceTasks.value = await response.json()
  } catch (error) {
    console.error('获取顺序任务失败:', error)
  }
}

/**
 * 安排新的DO控制任务
 */
async function scheduleNewTask() {
  if (newTask.delay_minutes <= 0) {
    alert('延迟分钟数必须大于0')
    return
  }
  const apiUrl = `${BASE_URL.value}/schedule/task`
  const requestBody = {
    sn: SN.value,
    do_name: newTask.do_name,
    value: newTask.value,
    delay_minutes: newTask.delay_minutes
  }
  try {
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    })
    const result = await response.json()
    if (!response.ok) {
      throw new Error(result.detail || `HTTP error! status: ${response.status}`)
    }
    alert(result.message)
    // 刷新任务列表
    fetchScheduledTasks()
  } catch (error) {
    console.error('安排任务失败:', error)
    alert(`安排任务失败: ${error}`)
  }
}

/**
 * 取消计划任务
 * @param jobId - 任务ID
 */
async function cancelScheduledTask(jobId: string) {
  const apiUrl = `${BASE_URL.value}/schedule/task/${jobId}`
  try {
    const response = await fetch(apiUrl, {
      method: 'DELETE'
    })
    const result = await response.json()
    if (!response.ok) {
      throw new Error(result.detail || `HTTP error! status: ${response.status}`)
    }
    alert(result.message)
    // 刷新任务列表
    fetchScheduledTasks()
  } catch (error) {
    console.error(`取消任务 ${jobId} 失败:`, error)
    alert(`取消任务 ${jobId} 失败: ${error}`)
  }
}

/**
 * 安排新的循环任务
 */
async function scheduleNewCycleTask() {
  if (newCycleTask.on_minutes <= 0 || newCycleTask.off_minutes <= 0) {
    alert('开启和关闭时长都必须大于0')
    return
  }
  const apiUrl = `${BASE_URL.value}/schedule/cycle`
  const requestBody = {
    sn: SN.value,
    do_name: newCycleTask.do_name,
    on_minutes: newCycleTask.on_minutes,
    off_minutes: newCycleTask.off_minutes
  }
  try {
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    })
    const result = await response.json()
    if (!response.ok) {
      throw new Error(result.detail || `HTTP error! status: ${response.status}`)
    }
    alert(result.message)
    fetchCyclicTasks() // 刷新列表
  } catch (error) {
    console.error('安排循环任务失败:', error)
    alert(`安排循环任务失败: ${error}`)
  }
}

/**
 * 取消循环任务
 * @param cycleId - 循环任务ID
 */
async function cancelCyclicTask(cycleId: string) {
  const apiUrl = `${BASE_URL.value}/schedule/cycle/${cycleId}`
  try {
    const response = await fetch(apiUrl, {
      method: 'DELETE'
    })
    const result = await response.json()
    if (!response.ok) {
      throw new Error(result.detail || `HTTP error! status: ${response.status}`)
    }
    alert(result.message)
    fetchCyclicTasks() // 刷新列表
  } catch (error) {
    console.error(`取消循环任务 ${cycleId} 失败:`, error)
    alert(`取消循环任务 ${cycleId} 失败: ${error}`)
  }
}

/**
 * 安排新的顺序任务
 */
async function scheduleNewSequenceTask() {
  if (
    newSequenceTask.do_a_minutes <= 0 ||
    newSequenceTask.do_b_minutes <= 0 ||
    newSequenceTask.do_a_name === newSequenceTask.do_b_name
  ) {
    alert('时长必须大于0，且两个设备不能相同。')
    return
  }
  const apiUrl = `${BASE_URL.value}/schedule/sequence`
  const requestBody = {
    sn: SN.value,
    ...newSequenceTask
  }
  try {
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    })
    const result = await response.json()
    if (!response.ok) {
      throw new Error(result.detail || `HTTP error! status: ${response.status}`)
    }
    alert(result.message)
    fetchSequenceTasks() // 刷新列表
  } catch (error) {
    console.error('安排顺序任务失败:', error)
    alert(`安排顺序任务失败: ${error}`)
  }
}

/**
 * 取消顺序任务
 * @param sequenceId - 顺序任务ID
 */
async function cancelSequenceTask(sequenceId: string) {
  const apiUrl = `${BASE_URL.value}/schedule/sequence/${sequenceId}`
  try {
    const response = await fetch(apiUrl, {
      method: 'DELETE'
    })
    const result = await response.json()
    if (!response.ok) {
      throw new Error(result.detail || `HTTP error! status: ${response.status}`)
    }
    alert(result.message)
    fetchSequenceTasks() // 刷新列表
  } catch (error) {
    console.error(`取消顺序任务 ${sequenceId} 失败:`, error)
    alert(`取消顺序任务 ${sequenceId} 失败: ${error}`)
  }
}

/**
 * 根据DO名称获取其标签
 * @param doName - DO的名称, e.g., 'DO21'
 * @returns 对应的标签, e.g., '水泵1'
 */
function getDoLabel(doName: string): string {
  const doItem = doControls.value.find((item) => item.name === doName)
  return doItem ? doItem.label : doName
}

/**
 * 获取旋钮开关的状态
 * @param deviceStatus - 设备状态对象
 * @returns 'auto', 'stop', 'manual', or null
 */
function getKnobStatus(deviceStatus?: DeviceStatus): 'auto' | 'stop' | 'manual' | null {
  if (!deviceStatus) return null
  if (deviceStatus.auto_status === 1) return 'auto'
  if (deviceStatus.stop_status === 1) return 'stop'
  if (deviceStatus.manual_status === 1) return 'manual'
  return null
}

// 使用 computed 属性来派生状态，使模板更简洁
const deviceControls = computed(() => {
  const c = deviceData.value?.dianliucaiji2
  return [
    {
      id: 'water_pump1',
      label: '水泵1',
      status: getKnobStatus(deviceData.value?.water_pump1),
      current: c?.curr1_ch1 ?? c?.curr2_ch1
    },
    {
      id: 'water_pump2',
      label: '水泵2',
      status: getKnobStatus(deviceData.value?.water_pump2),
      current: c?.curr1_ch2 ?? c?.curr2_ch2
    },
    {
      id: 'air_pump1',
      label: '气泵1',
      status: getKnobStatus(deviceData.value?.air_pump1),
      current: c?.curr1_ch3 ?? c?.curr2_ch3
    },
    {
      id: 'air_pump2',
      label: '气泵2',
      status: getKnobStatus(deviceData.value?.air_pump2),
      current: c?.curr1_ch4 ?? c?.curr2_ch4
    }
  ]
})

const temperatureHumidityStatus = computed(() => {
  const temp = deviceData.value?.wenshidu?.temperature
  const hum = deviceData.value?.wenshidu?.humidity
  return [
    { label: '温度', value: temp != null ? temp.toFixed(1) : '--', unit: '°C' },
    { label: '湿度', value: hum != null ? hum.toFixed(1) : '--', unit: '%' }
  ]
})

const floatSwitchStatus = computed(() => {
  const float1 = deviceData.value?.float_switches?.float1
  return [{ label: '状态', value: float1 == null ? '--' : float1 === 1 ? '运行' : '未运行', unit: '' }]
})

/**
 * 获取用电度数显示 - 对大船港村曹村使用reverse_active_energy
 */
function getEnergyDisplayForDevice(): string {
  const p = deviceData.value?.diannengbiao
  // 检查是否为大船港村曹村点位 (SN: 02800125071500004977)
  if (SN.value === '02800125071500004977') {
    // 对大船港村曹村使用reverse_active_energy字段
    return p?.reverse_active_energy?.toFixed(2) ?? '--';
  } else {
    // 其他点位使用active_energy字段
    return p?.active_energy?.toFixed(2) ?? '--';
  }
}

const powerMeterDetails = computed(() => {
  const p = deviceData.value?.diannengbiao
  return [
    { label: 'A相电流', value: p?.currents?.Ia?.toFixed(3), unit: 'A' },
    { label: 'B相电流', value: p?.currents?.Ib?.toFixed(3), unit: 'A' },
    { label: 'C相电流', value: p?.currents?.Ic?.toFixed(3), unit: 'A' },
    { label: '总有功功率', value: p?.active_power?.total?.toFixed(1), unit: 'W' },
    { label: '总无功功率', value: p?.reactive_power?.total?.toFixed(1), unit: 'var' },
    { label: '用电度数', value: getEnergyDisplayForDevice(), unit: 'kWh' }
  ]
})

const deviceInfo = computed(() => {
  const info = deviceData.value?.device_info
  const lastUpdated = deviceData.value?.last_updated
  return [
    { label: 'SN', value: info?.sn },
    { label: 'IMEI', value: info?.imei },
    { label: '固件版本', value: info?.fw_version },
    { label: '数据时间戳', value: lastUpdated }
  ]
})

// 新增计算属性和方法

// 系统状态计算
const getSystemStatus = () => {
  if (!deviceData.value) return '数据加载中'
  const hasError = deviceControls.value.some(device => device.status === null)
  return hasError ? '运行异常' : '运行正常'
}

const getSystemStatusClass = () => {
  return getSystemStatus() === '运行正常' ? 'status-normal' : 'status-error'
}

// 浮球状态文本和样式
const getFloatStatusText = () => {
  const float1 = deviceData.value?.float_switches?.float1
  if (float1 == null) return '--'
  return float1 === 1 ? '高水位（运行）' : '低水位（未运行）'
}

const getFloatStatusClass = () => {
  const float1 = deviceData.value?.float_switches?.float1
  if (float1 == null) return ''
  return float1 === 1 ? 'high-water' : 'low-water'
}

// 温湿度显示
const getTemperatureDisplay = () => {
  const temp = deviceData.value?.wenshidu?.temperature
  return temp != null ? `${temp.toFixed(1)}°C` : '--'
}

const getHumidityDisplay = () => {
  const hum = deviceData.value?.wenshidu?.humidity
  return hum != null ? `${hum.toFixed(1)}%` : '--'
}

// 设备状态列表
const deviceStatusList = computed(() => {
  const controls = deviceControls.value
  return controls.map(device => ({
    id: device.id,
    name: device.label,
    runStatus: getDeviceRunStatus(device),
    gearStatus: getDeviceGearStatus(device),
    current: device.current != null ? `${device.current.toFixed(3)}A` : '--',
    selfCheck: { text: '正常', class: 'check-normal' },
    lastCheckTime: new Date().toLocaleString()
  }))
})

// 获取设备运行状态
function getDeviceRunStatus(device: DeviceControlItem) {
  if (!device.status) return { text: '未知', class: 'status-unknown' }

  // 检查DO状态来判断运行状态
  const doStatusMap: Record<string, string> = {
    'water_pump1': 'DO21_status',
    'water_pump2': 'DO22_status',
    'air_pump1': 'DO23_status',
    'air_pump2': 'DO24_status'
  }

  const doStatusKey = doStatusMap[device.id]
  const isRunning = deviceData.value?.[doStatusKey as keyof DeviceData] === 1

  if (isRunning) {
    return { text: '运行中', class: 'status-running' }
  } else {
    return { text: '停止', class: 'status-stopped' }
  }
}

// 获取设备档位状态
function getDeviceGearStatus(device: DeviceControlItem) {
  if (!device.status) return { text: '未知', class: 'gear-unknown' }

  switch (device.status) {
    case 'auto':
      return { text: '自动', class: 'gear-auto' }
    case 'manual':
      return { text: '手动', class: 'gear-manual' }
    case 'stop':
      return { text: '停止', class: 'gear-stop' }
    default:
      return { text: '未知', class: 'gear-unknown' }
  }
}

// 能耗数据状态
const energyStats = ref<{
  statistics?: {
    today?: { consumption: number };
    yesterday?: { consumption: number };
    this_month?: { consumption: number };
    last_month?: { consumption: number }
  }
} | null>(null)

// 获取用电统计数据
async function fetchPowerConsumptionStats() {
  const apiUrl = `${BASE_URL.value}/power-consumption/stats?device_sn=${SN.value}`

  try {
    const response = await fetch(apiUrl)
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    energyStats.value = await response.json()
  } catch (error) {
    console.error('获取用电统计失败:', error)
    // 发生错误时energyStats保持null，显示"--"状态
  }
}

// 能耗数据（仅使用真实API数据）
const energyData = computed(() => {
  if (energyStats.value?.statistics) {
    const stats = energyStats.value.statistics
    return [
      {
        label: '今日用电',
        value: stats.today?.consumption?.toFixed(2) ?? '--',
        unit: '度'
      },
      {
        label: '昨日用电',
        value: stats.yesterday?.consumption?.toFixed(2) ?? '--',
        unit: '度'
      },
      {
        label: '本月用电',
        value: stats.this_month?.consumption?.toFixed(2) ?? '--',
        unit: '度'
      },
      {
        label: '上月用电',
        value: stats.last_month?.consumption?.toFixed(2) ?? '--',
        unit: '度'
      }
    ]
  }

  // 接口未响应时显示加载状态
  return [
    { label: '今日用电', value: '--', unit: '度' },
    { label: '昨日用电', value: '--', unit: '度' },
    { label: '本月用电', value: '--', unit: '度' },
    { label: '上月用电', value: '--', unit: '度' }
  ]
})

// 任务相关计算属性
const hasRunningTasks = computed(() => {
  return (activeTasksData.value?.active_tasks_count || 0) > 0
})

// 获取任务类型样式类
function getTaskTypeClass(taskType: string): string {
  switch (taskType) {
    case '循环任务':
      return 'task-cycle'
    case '顺序任务':
      return 'task-sequence'
    case '单次任务':
      return 'task-single'
    default:
      return 'task-default'
  }
}

// 获取任务摘要显示 - 基于新的活动任务接口
function getSingleTaskSummary(): string {
  // 单次任务现在通过 /schedule/tasks 接口获取，保持原有逻辑
  const tasks = Object.entries(scheduledTasks.value)
  if (tasks.length === 0) return '无'

  const task = tasks[0][1] as { run_date: string; args: [string, string, number]; remaining_minutes?: number; do_name?: string; value?: number }
  // 兼容旧数据结构和新数据结构
  const remainingMinutes = task.remaining_minutes || 0
  const doName = task.do_name || (task.args && task.args[1]) || ''
  const value = task.value !== undefined ? task.value : (task.args && task.args[2])

  if (remainingMinutes <= 0) return '无'

  return `${getDoLabel(doName)} ${remainingMinutes}'后${value === 1 ? '开启' : '关闭'}`
}

function getCycleTaskSummary(): string {
  // 使用新的活动任务接口数据
  if (!activeTasksData.value?.active_tasks) return '无'

  const cycleTasks = activeTasksData.value.active_tasks.filter(task => task.type === 'cycle')
  if (cycleTasks.length === 0) return '无'

  const task = cycleTasks[0]
  if (!task.cycle_info) return '无'

  const onHours = Math.floor(task.cycle_info.on_minutes / 60)
  const onMins = task.cycle_info.on_minutes % 60
  const offHours = Math.floor(task.cycle_info.off_minutes / 60)
  const offMins = task.cycle_info.off_minutes % 60

  const onDisplay = onHours > 0 ? `${onHours}h${onMins > 0 ? onMins + 'm' : ''}` : `${onMins}m`
  const offDisplay = offHours > 0 ? `${offHours}h${offMins > 0 ? offMins + 'm' : ''}` : `${offMins}m`

  return `${task.device_name} ${onDisplay}/${offDisplay}`
}

function getSequenceTaskSummary(): string {
  // 使用新的活动任务接口数据
  if (!activeTasksData.value?.active_tasks) return '无'

  const sequenceTasks = activeTasksData.value.active_tasks.filter(task => task.type === 'sequence')
  if (sequenceTasks.length === 0) return '无'

  const task = sequenceTasks[0]
  if (!task.sequence_info) return '无'

  const aHours = Math.floor(task.sequence_info.do_a_minutes / 60)
  const aDisplay = aHours > 0 ? `${aHours}h` : `${task.sequence_info.do_a_minutes}m`

  return `${task.sequence_info.device_a}↔${task.sequence_info.device_b} ${aDisplay}轮换`
}

// 获取当前活动任务的显示信息
function getActiveTasksDisplay() {
  if (!activeTasksData.value?.active_tasks) {
    return []
  }

  return activeTasksData.value.active_tasks.map(task => ({
    id: task.id,
    deviceName: task.device_name,
    statusText: task.status_text,
    remainingTime: task.remaining_time !== '运行中' ? task.remaining_time : undefined,
    type: task.type === 'cycle' ? '循环任务' : '顺序任务',
    nextAction: task.next_action,
    deviceSn: task.device_sn,
    isHealthy: task.type === 'sequence' ? task.sequence_info?.healthy : true
  }))
}

// 获取设备类型样式
function getDeviceTypeClass(deviceName: string) {
  if (deviceName.includes('水泵')) {
    return 'water-pump'
  } else if (deviceName.includes('气泵')) {
    return 'air-pump'
  }
  return ''
}

// 获取设备额定电压
function getDeviceVoltage(deviceName: string) {
  if (deviceName.includes('水泵')) {
    return '380V'
  } else if (deviceName.includes('气泵')) {
    return '220V'
  }
  return ''
}

// 任务操作方法
// eslint-disable-next-line @typescript-eslint/no-unused-vars
function modifyTask(_taskId: string) {
  alert('修改任务功能开发中...')
}

function cancelTask(taskId: string, taskType: string) {
  switch (taskType) {
    case '单次任务':
      cancelScheduledTask(taskId)
      break
    case '循环任务':
      cancelCyclicTask(taskId)
      break
    case '顺序任务':
      cancelSequenceTask(taskId)
      break
    default:
      console.warn('未知的任务类型:', taskType)
      alert('无法取消未知类型的任务')
  }
}
</script>

<style scoped>
/* 新的页面样式 */

/* 顶部信息区样式 */
.top-info-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.station-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.station-title h1 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
}

.station-meta {
  display: flex;
  gap: 10px;
  font-size: 12px;
  opacity: 0.9;
}

.station-status {
  padding: 1px 6px;
  border-radius: 8px;
  font-size: 10px;
  font-weight: 500;
}

.status-normal {
  background-color: rgba(40, 167, 69, 0.8);
}

.status-error {
  background-color: rgba(220, 53, 69, 0.8);
}

/* 新的集成信息样式 */
.integrated-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-row {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.info-label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 11px;
}

.info-value {
  color: white;
  font-weight: 600;
  font-size: 12px;
}

.high-water {
  color: #ff6b6b;
}

.low-water {
  color: #4ecdc4;
}

/* 设备运行状态区样式 - 紧凑布局 */
.device-status-section,
.energy-dashboard-section,
.task-schedule-section {
  margin-bottom: 8px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 6px 0;
  color: var(--text-color);
}

.subsection-title {
  font-size: 13px;
  font-weight: 500;
  margin: 0 0 6px 0;
  color: var(--text-color);
}

/* 设备运行状态区样式 - 田字形网格布局 */
.device-status-section,
.energy-dashboard-section,
.task-schedule-section {
  margin-bottom: 8px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 6px 0;
  color: var(--text-color);
}

.subsection-title {
  font-size: 13px;
  font-weight: 500;
  margin: 0 0 6px 0;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 8px;
}

.task-count-badge {
  background-color: #007bff;
  color: white;
  font-size: 10px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
}

.device-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.device-compact-card {
  background: white;
  border-radius: 6px;
  padding: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  position: relative;
}

.device-compact-card.water-pump {
  border-left: 4px solid #007bff;
  background: linear-gradient(135deg, #f8fbff 0%, #e3f2fd 100%);
}

.device-compact-card.air-pump {
  border-left: 4px solid #28a745;
  background: linear-gradient(135deg, #f1f8e9 0%, #e8f5e8 100%);
}

.device-header-compact {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 4px;
}

.device-name-section {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.device-name-compact {
  font-weight: 600;
  font-size: 13px;
  color: var(--text-color);
}

.device-voltage {
  font-size: 9px;
  color: var(--text-light-color);
  background-color: rgba(0, 123, 255, 0.1);
  padding: 1px 4px;
  border-radius: 8px;
  margin-top: 1px;
}

.air-pump .device-voltage {
  background-color: rgba(40, 167, 69, 0.1);
}

.device-current-compact {
  font-size: 11px;
  font-weight: 600;
  color: var(--primary-color);
  background-color: rgba(0, 123, 255, 0.05);
  padding: 2px 6px;
  border-radius: 8px;
  border: 1px solid rgba(0, 123, 255, 0.2);
}

.air-pump .device-current-compact {
  color: var(--success-color);
  background-color: rgba(40, 167, 69, 0.05);
  border-color: rgba(40, 167, 69, 0.2);
}

.device-status-compact {
  display: flex;
  gap: 8px;
  margin-bottom: 4px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 2px;
}

.status-label {
  font-size: 9px;
  color: var(--text-light-color);
}

.status-value {
  font-size: 10px;
  font-weight: 500;
}

.device-check-compact {
  font-size: 8px;
  color: var(--text-light-color);
  text-align: center;
  padding-top: 2px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 4px;
}

.self-check-result {
  font-size: 8px;
  font-weight: 600;
  padding: 1px 4px;
  border-radius: 4px;
}

.check-normal {
  color: #28a745;
  background-color: rgba(40, 167, 69, 0.1);
}

.check-error {
  color: #dc3545;
  background-color: rgba(220, 53, 69, 0.1);
}

/* 状态样式 */
.status-running {
  color: #28a745;
}

.status-stopped {
  color: #6c757d;
}

.status-unknown {
  color: #ffc107;
}

.gear-auto {
  color: #007bff;
}

.gear-manual {
  color: #28a745;
}

.gear-stop {
  color: #6c757d;
}

.gear-unknown {
  color: #ffc107;
}

/* 能耗数据看板样式 */
.energy-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 4px;
}

.energy-card {
  background: white;
  border-radius: 6px;
  padding: 6px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  text-align: center;
  border: 1px solid #f0f0f0;
}

.energy-label {
  font-size: 8px;
  color: var(--text-light-color);
  margin-bottom: 2px;
}

.energy-value {
  font-size: 12px;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 1px;
}

.energy-unit {
  font-size: 8px;
  color: var(--text-light-color);
}

/* 任务调度区样式 */
/* 任务调度区样式 */
.task-summary {
  background: white;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 12px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

.task-summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.task-summary-item:last-child {
  border-bottom: none;
}

.task-type-label {
  font-size: 12px;
  font-weight: 600;
  color: #333;
  min-width: 60px;
}

.task-summary-info {
  font-size: 11px;
  color: #666;
  flex: 1;
  text-align: right;
}

.running-tasks {
  background: white;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 12px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

.active-task-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.active-task-item {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 10px;
  border-left: 3px solid #007bff;
  transition: all 0.3s ease;
}

.active-task-item.task-unhealthy {
  border-left-color: #dc3545;
  background: #fff5f5;
}

.task-main-info {
  margin-bottom: 8px;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.task-device {
  font-size: 13px;
  font-weight: 600;
  color: #333;
}

.task-type-badge {
  font-size: 9px;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 8px;
  text-transform: uppercase;
}

.task-type-badge.task-cycle {
  background-color: #e3f2fd;
  color: #1976d2;
}

.task-type-badge.task-sequence {
  background-color: #e8f5e8;
  color: #388e3c;
}

.task-type-badge.task-single {
  background-color: #fff3e0;
  color: #f57c00;
}

.task-status-text {
  font-size: 11px;
  color: #666;
  margin-bottom: 4px;
}

.task-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.task-remaining {
  font-size: 10px;
  color: #007bff;
  font-weight: 500;
}

.task-next-action {
  font-size: 10px;
  color: #28a745;
  font-weight: 500;
}

.task-health {
  margin-top: 2px;
}

.health-warning {
  font-size: 10px;
  color: #dc3545;
  font-weight: 600;
}

.task-action-buttons {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.cancel-slide-btn,
.modify-task-btn {
  padding: 4px 8px;
  border: none;
  border-radius: 4px;
  font-size: 10px;
  cursor: pointer;
  transition: all 0.3s;
}

.cancel-slide-btn {
  background-color: #dc3545;
  color: white;
}

.cancel-slide-btn:hover {
  background-color: #c82333;
}

.modify-task-btn {
  background-color: #6c757d;
  color: white;
}

.modify-task-btn:hover {
  background-color: #5a6268;
}

.running-task-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
  border-bottom: 1px solid #f0f0f0;
}

.running-task-item:last-child {
  border-bottom: none;
}

.task-info {
  flex: 1;
}

.task-type-badge {
  display: inline-block;
  padding: 1px 4px;
  border-radius: 8px;
  font-size: 8px;
  font-weight: 500;
  margin-bottom: 2px;
}

.task-single {
  background-color: #e3f2fd;
  color: #1976d2;
}

.task-cycle {
  background-color: #f3e5f5;
  color: #7b1fa2;
}

.task-sequence {
  background-color: #e8f5e8;
  color: #388e3c;
}

.task-description {
  font-size: 11px;
  font-weight: 500;
  margin-bottom: 1px;
}

.task-status {
  font-size: 9px;
  color: var(--text-light-color);
}

.task-actions {
  display: flex;
  gap: 4px;
}

.task-modify-btn,
.task-cancel-btn {
  padding: 2px 6px;
  border: none;
  border-radius: 3px;
  font-size: 9px;
  cursor: pointer;
}

.task-modify-btn {
  background-color: #007bff;
  color: white;
}

.task-cancel-btn {
  background-color: #dc3545;
  color: white;
}

.task-create-section {
  text-align: center;
}

.new-task-btn {
  background-color: #28a745;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s;
}

.new-task-btn:hover {
  background-color: #218838;
}

/* 高级控制区样式 */
.advanced-controls-section {
  background: white;
  border-radius: 6px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  margin-bottom: 8px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
}

.section-header:hover {
  background-color: #f8f9fa;
}

.toggle-icon {
  transition: transform 0.3s ease;
  font-size: 12px;
  color: var(--text-light-color);
}

.toggle-icon.expanded {
  transform: rotate(180deg);
}

.advanced-controls-content {
  padding: 12px;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .container {
    padding: 4px;
  }

  .device-grid {
    grid-template-columns: 1fr 1fr;
    gap: 6px;
  }

  .energy-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 2px;
  }

  .station-header {
    flex-direction: column;
    gap: 8px;
  }

  .header-actions {
    align-self: stretch;
  }

  .header-button {
    font-size: 10px;
    padding: 3px 6px;
  }

  .info-row {
    flex-wrap: wrap;
    gap: 4px;
  }
}

/* 原有样式保持不变 */
:root {
  --primary-color: #007bff;
  --secondary-color: #6c757d;
  --success-color: #28a745;
  --danger-color: #dc3545;
  --warning-color: #ffc107;
  --bg-color: #ffffff;
  --card-bg-color: #ffffff;
  --card-header-bg-color: #f8f9fa;
  --text-color: #333;
  --text-light-color: #666;
}

.container {
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
  padding: 8px;
  background-color: var(--bg-color);
}

.card {
  background-color: var(--card-bg-color);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
  overflow: hidden;
}

.card-header {
  background-color: var(--card-header-bg-color);
  padding: 12px 20px;
  font-weight: bold;
  color: var(--text-color);
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-body {
  padding: 20px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
}

.info-item {
  text-align: center;
}

.info-item .label {
  font-size: 14px;
  color: var(--text-light-color);
  margin-bottom: 8px;
}

.info-item .value {
  font-size: 24px;
  font-weight: bold;
  color: var(--text-color);
}

.info-item .unit {
  font-size: 14px;
  margin-left: 4px;
}

.device-info-list,
.power-details-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0 20px;
}

.device-info-list li,
.power-details-list li {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.device-info-list li:last-child:nth-child(odd),
.power-details-list li:last-child:nth-child(odd) {
  grid-column: span 2;
}

.device-info-list li:last-child,
.power-details-list li:last-child {
  border-bottom: none;
}

.device-info-list .label,
.power-details-list .label {
  color: var(--text-light-color);
}

.device-info-list .value,
.power-details-list .value {
  font-weight: 500;
}

/* 旋钮开关样式 */
.device-control-item {
  margin-bottom: 20px;
}

.device-control-item:last-child {
  margin-bottom: 0;
}

.device-header {
  display: flex;
  justify-content: space-between;
  align-items: baseline;
  margin-bottom: 10px;
}

.device-label {
  font-weight: 500;
  font-size: 16px;
}

.device-current {
  font-size: 14px;
  color: var(--text-light-color);
}

.device-current strong {
  color: var(--text-color);
  font-weight: 600;
}

.knob-switch {
  display: flex;
  justify-content: space-around;
  background-color: #e9ecef;
  border-radius: 8px;
  padding: 4px;
}

.knob-switch-option {
  flex: 1;
  padding: 8px;
  text-align: center;
  border-radius: 6px;
  font-weight: 500;
  cursor: default;
  /* 改为默认指针，表示不可点击 */
  transition:
    background-color 0.3s,
    color 0.3s;
  color: var(--text-light-color);
}

.knob-switch-option.active {
  background-color: var(--card-bg-color);
  color: var(--text-color);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.knob-switch-option.active[data-state='auto'] {
  background-color: var(--primary-color);
  color: #fff;
}

.knob-switch-option.active[data-state='stop'] {
  background-color: var(--secondary-color);
  color: #fff;
}

.knob-switch-option.active[data-state='manual'] {
  background-color: var(--success-color);
  color: #fff;
}

/* DO 直控样式 */
.do-control-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.do-control-list li {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.do-control-list li:last-child {
  border-bottom: none;
}

.do-control-list .label {
  font-weight: 500;
}

.do-control-list .actions .btn {
  margin-left: 8px;
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  color: #fff;
  transition: opacity 0.3s;
}

.do-control-list .actions .btn:hover {
  opacity: 0.8;
}

.btn-on {
  background-color: #198754;
}

.btn-off {
  background-color: #dc3545;
}

/* 定时任务样式 */
.task-type-switcher {
  display: flex;
  margin-bottom: 20px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #ced4da;
}

.task-type-switcher button {
  flex: 1;
  padding: 10px;
  border: none;
  background-color: #f8f9fa;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: #343a40;
  transition: background-color 0.3s, color 0.3s;
}

.task-type-switcher button.active {
  background-color: #007bff;
  color: #ffffff;
}

.task-scheduler-form {
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e9ecef;
}

.form-title,
.list-title {
  font-size: 16px;
  margin-top: 0;
  margin-bottom: 16px;
  font-weight: bold;
}

.form-group {
  margin-bottom: 12px;
  display: flex;
  flex-direction: column;
}

.form-group label {
  margin-bottom: 6px;
  font-size: 14px;
  color: var(--text-light-color);
}

.form-group select,
.form-group input {
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid #ced4da;
  font-size: 14px;
}

.btn-submit {
  width: 100%;
  padding: 10px;
  background-color: #0d6efd;
  color: #fff;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  cursor: pointer;
  margin-top: 8px;
  transition: background-color 0.3s;
}

.btn-submit:hover {
  background-color: #0b5ed7;
}

.scheduled-tasks-list ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.scheduled-tasks-list li {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
  font-size: 14px;
}

.scheduled-tasks-list li:last-child {
  border-bottom: none;
}

.btn-cancel {
  padding: 4px 8px;
  font-size: 12px;
  background-color: #ffc107;
  color: #000;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: opacity 0.3s;
}

.btn-cancel:hover {
  opacity: 0.8;
}

/* 头部按钮样式 */
.header-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.header-button {
  padding: 4px 8px;
  font-size: 11px;
  font-weight: 500;
  background-color: transparent;
  color: blue;
  border: 1px solid var(--primary-color);
  text-decoration: none;
  border-radius: 4px;
  transition: background-color 0.3s, color 0.3s;
  cursor: pointer;
}

.header-button:hover {
  background-color: var(--primary-color);
  color: white;
}

.back-button {
  background-color: #6c757d;
  color: white;
  border-color: #6c757d;
}

.back-button:hover {
  background-color: #5a6268;
  border-color: #5a6268;
}

.station-info {
  margin-top: 4px;
}

.station-info small {
  color: #6c757d;
  font-size: 12px;
}

.form-description {
  font-size: 13px;
  color: #6c757d;
  margin-top: -10px;
  margin-bottom: 16px;
}

.form-error-message {
  font-size: 13px;
  color: #dc3545;
  /* 红色醒目提示 */
  margin-top: 10px;
  text-align: center;
}

.non-clickable-note {
  font-size: 13px;
  color: #6c757d;
  text-align: center;
  margin-top: 24px;
  padding: 8px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.section-header .new-task-btn {
  font-size: 0.9rem;
  padding: 0.5rem 1rem;
}
</style>

# 水利智能管理平台 - 前端应用

始终使用中文和我交流

## 项目概述

水利智能管理平台前端是一个基于 Vue 3 的 H5 移动端应用，专为水利站的水泵和气泵设备监控管理而设计。该应用提供实时设备状态监控、手动设备控制、智能任务调度和历史数据查询等核心功能。

## 技术架构

- **框架**: Vue 3 + TypeScript (Composition API)
- **构建工具**: Vite
- **状态管理**: Pinia (轻量级状态管理)
- **路由**: Vue Router (Hash 模式，兼容设备部署)
- **UI 设计**: 自定义 CSS，卡片式布局，移动端优化
- **开发工具**: ESLint + Prettier + Vitest

## 核心功能模块

### 1. 设备状态监控面板 (`DeviceStatusView.vue`)

**主要功能**:
- **设备实时监控**: 4台设备(水泵1、水泵2、气泵1、气泵2)的运行状态和电流监测
- **旋钮状态显示**: 可视化显示设备当前档位(自动/停止/手动)
- **环境传感器**: 实时温湿度数据展示
- **浮球开关**: 水位传感器状态监控
- **电力参数**: 三相电压电流、总功率、用电度数等电能表数据
- **设备信息**: 设备SN、固件版本、IMEI、数据时间戳

**操作功能**:
- **DO直控**: 4路数字输出开关的手动控制(开启/关闭)
- **任务调度**: 支持三种类型的定时任务创建和管理
- **活动任务监控**: 实时显示当前运行的所有任务

### 2. 任务调度系统

**任务类型**:

#### 单次任务
- 延时执行指定设备的开启/关闭操作
- 可设置延迟时间(分钟)
- 自动验证设备是否处于自动模式

#### 循环任务  
- 设备按设定周期自动开启和关闭
- 可配置开启时长和关闭时长
- 无限循环执行直到手动取消

#### 顺序任务
- 两个设备交替运行的循环模式
- A设备运行指定时间后关闭，立即启动B设备
- B设备运行完成后重新启动A设备，无限循环

#### 水泵配置管理
- 设置水泵1运行多久后自动启动水泵2
- 默认2小时轮换，可自定义延时时间
- 支持配置重置功能

**任务管理**:
- 实时创建、取消任务
- 任务执行状态监控
- 智能表单验证(仅自动模式设备可调度)

### 3. 历史数据查询 (`HistoryView.vue`)

**数据筛选功能**:
- **时间范围筛选**: 支持日期时间区间查询
- **设备状态筛选**: 浮球状态、各设备运行状态
- **传感器数值筛选**: 温湿度、电压、电流的范围筛选
- **关键字搜索**: 在历史数据中进行文本搜索
- **多条件组合**: 支持多个筛选条件同时应用

**数据展示**:
- 分页浏览历史记录(每页10条)
- 详细的设备状态和传感器数据
- 灵活的页面跳转和导航
- 状态可视化显示(颜色区分不同状态)

## 设备控制协议

### DO控制映射
- **DO21**: 水泵1 (Water Pump 1)
- **DO22**: 水泵2 (Water Pump 2)  
- **DO23**: 气泵1 (Air Pump 1)
- **DO24**: 气泵2 (Air Pump 2)

### 设备状态模式
每个设备支持三种工作模式(由物理旋钮开关控制):
- `auto_status`: 自动模式 - 可接受系统调度控制
- `stop_status`: 停止模式 - 设备强制停止
- `manual_status`: 手动模式 - 人工现场操作

## API接口集成

**服务器地址**: `http://49.235.191.145:8500`  
**设备序列号**: `02801925060700002997`

### 核心接口
- `GET /data/{SN}`: 获取设备实时数据和传感器读数
- `POST /control/{SN}`: 执行DO开关控制命令
- `POST /schedule/task`: 创建单次延时任务
- `POST /schedule/cycle`: 创建循环任务
- `POST /schedule/sequence`: 创建顺序交替任务
- `GET /history/{SN}`: 查询历史数据(支持多种筛选参数)
- `GET /schedule/tasks`: 获取当前单次任务列表
- `GET /schedule/cycles`: 获取当前循环任务列表
- `GET /schedule/sequences`: 获取当前顺序任务列表
- `PUT /pump-config`: 更新水泵配置参数
- `GET /pump-config`: 获取当前水泵配置

## 数据结构定义

### 主要数据类型
```typescript
interface DeviceData {
  // 浮球开关状态
  float_switches: { float1: number };
  
  // 设备控制状态
  water_pump1: DeviceStatus;
  water_pump2: DeviceStatus;
  air_pump1: DeviceStatus;
  air_pump2: DeviceStatus;
  
  // 电流采集数据
  dianliucaiji2: {
    curr1_ch1: number; // 水泵1电流
    curr1_ch2: number; // 水泵2电流
    curr1_ch3: number; // 气泵1电流
    curr1_ch4: number; // 气泵2电流
  };
  
  // 温湿度传感器
  wenshidu: {
    temperature: number;
    humidity: number;
  };
  
  // 电能表数据
  diannengbiao: {
    voltages: { Ua: number; Ub: number; Uc: number };
    currents: { Ia: number; Ib: number; Ic: number };
    active_power: { total: number };
    active_energy: number;
  };
  
  // DO状态指示
  DO21_status: number; // 水泵1状态
  DO22_status: number; // 水泵2状态
  DO23_status: number; // 气泵1状态
  DO24_status: number; // 气泵2状态
  
  // 设备信息和时间戳
  device_info: { sn: string; fw_version: string; imei: string };
  last_updated: string;
  timestamp: string;
}

interface DeviceStatus {
  auto_status: number;   // 自动模式状态
  stop_status: number;   // 停止模式状态
  manual_status: number; // 手动模式状态
}
```

## 页面布局设计

### 主控制面板布局
1. **页面头部**: 项目标题 + 历史数据入口
2. **设备控制区**: 4台设备的状态卡片和旋钮显示
3. **传感器数据区**: 浮球状态、温湿度、电能表数据
4. **操作控制区**: DO直控按钮面板
5. **任务调度区**: 任务类型切换 + 创建表单
6. **活动任务区**: 当前运行任务列表
7. **设备信息区**: 设备详细信息展示

### 历史数据页面布局
1. **导航头部**: 返回按钮 + 页面标题
2. **筛选面板**: 可展开的多条件筛选器
3. **数据列表**: 分页显示的历史记录卡片
4. **分页导航**: 上一页/下一页 + 页面跳转

## 开发命令

```bash
# 安装依赖
npm install

# 开发服务器
npm run dev

# 生产构建
npm run build

# 类型检查
npm run type-check

# 代码检查
npm run lint

# 单元测试
npm run test:unit

# 预览构建
npm run preview

# 代码格式化
npm run format
```

## 关键实现特性

### 用户体验优化
- **30秒自动刷新**: 保持数据实时性
- **智能表单验证**: 防止在非自动模式下创建任务
- **移动端适配**: 响应式设计，适配各种屏幕尺寸
- **状态色彩区分**: 不同设备状态用颜色直观区分
- **操作反馈**: 完整的成功/失败消息提示

### 安全性设计
- **模式检查**: 仅允许自动模式设备接受调度
- **数据验证**: 前端表单验证 + 后端接口验证双重保护
- **错误处理**: 完善的异常捕获和用户友好的错误提示

### 性能优化
- **数据缓存**: 合理使用 Vue 响应式系统缓存数据
- **按需加载**: 路由级别的代码分割
- **TypeScript**: 完整类型定义，提供开发时类型检查

## 测试和质量保证

- **单元测试**: Vitest 测试框架
- **类型检查**: Vue TSC 严格模式
- **代码规范**: ESLint + Prettier 自动格式化
- **测试文件**: `*.test.ts` 或 `*.spec.ts` 命名规范

## 部署说明

- **构建输出**: `dist/` 目录，可直接部署到静态服务器
- **兼容性**: 支持现代浏览器，移动端优先设计
- **CORS配置**: 后端已配置跨域支持
- **生产优化**: Vite 自动进行代码压缩和优化
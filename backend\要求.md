1.先分析目前项目中有哪些调度器，再将目前项目中对设备远程进行状态维持的代码全都注释掉，设备本地运行自动化控制程序，不需要服务器远程下发，只保留远程控制设备的接口

✅ **已完成**：
- 分析了项目中的调度器：APScheduler任务调度器、状态监控服务、数据降采样服务
- 注释掉了TCP服务器中的设备远程状态维持逻辑
- 禁用了状态监控的自动修复功能
- 注释掉了main.py中的状态监控和任务调度器启动
- 保留了手动控制接口：/control/{sn}、/air-pump/{sn}/start、/air-pump/{sn}/stop等
- 所有设备现在采用本地自主控制，服务器仅保留手动控制功能

2.设备的物理开关档位变换导致设备运行状态的改变会被m300上报上来，作为设备操作日志记录到日志文件中，这个记录不要存入到MongoDB中，直接存入到logs文件夹中，并清除掉MongoDB中已经存在的相关设备操作日志数据

✅ **已完成**：
- 创建了专门的设备状态变化日志记录模块 `device_status_logger.py`
- 实现了设备状态变化检测功能，监控DO状态、水泵状态等关键字段的变化
- 设备状态变化日志直接存储到 `logs/device_status_changes_YYYYMMDD.log` 文件中
- 修改了 `generic_tcp_server.py`，集成设备状态变化检测功能
- 成功清理了MongoDB中的31,694条操作日志记录
- MongoDB现在仅保留设备数据记录用于数据降采样服务
- 设备状态变化记录格式包含：时间戳、设备SN、变化详情、变化类型等信息

3.调整device_status_logger.py中生成到日志名和日志位置，参考realtime_data_logger.py中的实现，将日志文件生成到 `logs/device_status/YYYYMMDD.log` 中，日志文件名改为 `device_status_YYYYMMDD.log`，同时修改测试文件simulation_test.py，仿真设备物理开关档位变换状态

✅ **已完成**：
- 调整了 `device_status_logger.py` 的日志文件路径和命名：
  - 日志目录改为：`logs/device_status/`
  - 日志文件名改为：`device_status_YYYYMMDD.log`
  - 参考了 `realtime_data_logger.py` 的实现方式
- 修改了设备状态日志记录器以支持单独发送的状态变化消息：
  - 增加了 `is_status_only` 参数来区分单独的状态变化消息和完整设备数据
  - 单独的状态变化消息会被标记为"设备物理开关档位变换"
  - 完整设备数据中的状态变化会被标记为"设备状态数据变化"
- 修改了 `generic_tcp_server.py` 来检测和处理单独的状态变化消息：
  - 检测只包含状态字段而不包含完整设备数据的消息
  - 自动识别物理开关档位变换消息并进行相应处理
- 修改了 `simulation_test.py` 仿真测试：
  - 增加了设备状态缓存来模拟真实的状态变化
  - 实现了单独的状态变化消息发送功能
  - 10%概率模拟DO状态变化，8%概率模拟水泵状态变化
  - 状态变化时会发送单独的状态变化消息到服务器
- 日志记录区分了两种类型的状态变化：
  - "设备物理开关档位变换"：设备主动发送的状态变化
  - "设备状态数据变化"：在完整数据中检测到的状态变化

  4.加入rabbitMQ 服务,获取的数据一方面存入mongodb数据库，同时推送给rabbitMQ消息队列。然后单独使用一个python程序，单例程序来定时（比如1秒）从消息队列获取 最多1000条数据，批量存入日志文件，这样避免了多线程写文件的问题

✅ **已完成**：
- 创建了 `rabbitmq_publisher.py` RabbitMQ消息发布者模块：
  - 实现单例模式的RabbitMQ发布者
  - 支持设备数据和状态变化两种消息类型的发布
  - 自动重连机制和错误处理
  - 消息持久化和JSON格式化
- 创建了 `log_processor.py` 单例日志处理程序：
  - 独立的Python程序，可单独运行
  - 从RabbitMQ队列消费消息，每1秒处理最多1000条数据
  - 批量写入日志文件，避免多线程写文件冲突
  - 按消息类型分别写入realtime_data和device_status日志
  - 优雅关闭机制，处理剩余缓冲区消息
- 修改了 `generic_tcp_server.py` 集成RabbitMQ推送：
  - 接收设备数据后同时存入MongoDB和推送到RabbitMQ
  - 检测状态变化后推送状态变化消息到RabbitMQ
  - 添加异常处理，RabbitMQ推送失败不影响核心功能
- 更新了 `docker-compose.yml` 添加RabbitMQ服务：
  - 使用rabbitmq:3.12-management镜像
  - 配置AMQP端口5672和管理界面端口15672
  - 数据持久化和健康检查
- 更新了 `requirements.txt` 添加pika==1.3.2依赖

**使用方法**：
1. 启动RabbitMQ服务：`docker-compose up rabbitmq -d`
2. 运行主程序：`python main.py`（自动推送消息到队列）
3. 单独运行日志处理器：`python log_processor.py`（从队列消费并批量写日志）

**系统架构**：
```
设备数据 → TCP服务器 → MongoDB存储 + RabbitMQ队列推送
                                    ↓
                      日志处理器 ← RabbitMQ队列消费 → 批量写入日志文件
```

5.帮我测试当前数据接收的流程
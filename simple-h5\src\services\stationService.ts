import { TokenManager } from '@/utils/tokenManager'
import type { StationListResponse, StationDevice } from '@/types/station'

// 站点信息API服务
export class StationService {
  private static readonly BASE_URL = 'https://api-dm.usr.cn/dmCloud'

  // 获取所有站点设备列表
  static async getStationList(): Promise<StationDevice[]> {
    try {
      const token = await TokenManager.getToken()
      const userId = TokenManager.getUserId()
      
      // 使用与网页端相同的请求格式
      const requestBody = {
        appointLoraNodeDevice: 1,
        appointSubNodeDevice: 2, 
        devicesTagParamDtos: [],
        pageNo: 1,
        pageSize: 10,
        projectId: "",
        searchParam: "",
        sortByWeight: "up",
        token: token
      }

      // 使用与网页端相同的请求头
      const headers = {
        'Content-Type': 'application/json',
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Origin': 'https://dm.usr.cn',
        'Referer': 'https://dm.usr.cn/',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
        'appid': 'cG8yNDYwODl6a2xqYWdpdXNkZ3E=',
        'languagetype': '0',
        'token': token,
        'traceid': userId,
        'u-source': 'in-pc',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site'
      }

      const response = await fetch(`${this.BASE_URL}/dev/getDevs`, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify(requestBody)
      })

      const result: StationListResponse = await response.json()
      
      if (result.status === 0 && result.data) {
        return result.data.list
      } else {
        throw new Error(result.info || '获取站点列表失败')
      }
    } catch (error) {
      console.error('获取站点列表失败:', error)
      throw error
    }
  }

  // 根据设备序列号获取单个设备信息
  static async getStationById(deviceNo: string): Promise<StationDevice | undefined> {
    try {
      const stations = await this.getStationList()
      return stations.find(station => station.deviceNo === deviceNo || station.sn === deviceNo)
    } catch (error) {
      console.error('获取单个站点信息失败:', error)
      throw error
    }
  }

  // 获取在线设备列表
  static async getOnlineStations(): Promise<StationDevice[]> {
    try {
      const stations = await this.getStationList()
      return stations.filter(station => station.deviceStatus.onlineOffline === 1)
    } catch (error) {
      console.error('获取在线设备列表失败:', error)
      throw error
    }
  }

  // 根据项目名称分组获取站点
  static async getStationsByProject(): Promise<Map<string, StationDevice[]>> {
    try {
      const stations = await this.getStationList()
      const groupedStations = new Map<string, StationDevice[]>()
      
      stations.forEach(station => {
        const projectName = station.projectName
        if (!groupedStations.has(projectName)) {
          groupedStations.set(projectName, [])
        }
        groupedStations.get(projectName)?.push(station)
      })
      
      return groupedStations
    } catch (error) {
      console.error('按项目分组获取站点失败:', error)
      throw error
    }
  }
}
# Node-RED 项目文档中心

## 项目概述

本项目提供了完整的 Node-RED 使用文档和示例，帮助用户快速上手和深入理解 Node-RED 的可视化编程能力。

## 📚 文档目录

### 核心文档
- **[Node-RED 完整使用指南](docs/Node-RED完整使用指南.md)** - 全面的使用指南，包含基础概念、安装配置、最佳实践等
- **[Node-RED 快速入门指南](docs/Node-RED快速入门指南.md)** - 5分钟快速上手，适合初学者
- **[Node-RED 节点参考手册](docs/Node-RED节点参考手册.md)** - 详细的节点配置参数和使用示例

### 原始参考资料
- `node-red使用说明.pdf` - 原始使用说明文档
- `node-red节点说明.pdf` - 原始节点说明文档

## 🚀 快速开始

### 1. 安装 Node-RED
```bash
# 使用 npm 安装
npm install -g node-red

# 启动 Node-RED
node-red
```

### 2. 访问编辑器
打开浏览器访问：`http://localhost:1880`

### 3. 创建第一个流程
参考 [快速入门指南](docs/Node-RED快速入门指南.md) 创建您的第一个 Node-RED 流程。

## 📁 项目结构

```
m300/
├── docs/                          # 文档目录
│   ├── Node-RED完整使用指南.md     # 完整使用指南
│   ├── Node-RED快速入门指南.md     # 快速入门指南
│   └── Node-RED节点参考手册.md     # 节点参考手册
├── flows/                         # 流程示例目录
├── scripts/                       # 脚本文件目录
├── config/                        # 配置文件目录
├── node-red使用说明.pdf           # 原始使用说明
├── node-red节点说明.pdf           # 原始节点说明
└── README.md                      # 项目说明文档
```

## 🎯 适用场景

Node-RED 特别适用于以下场景：

- **IoT 设备连接** - 连接传感器、执行器等硬件设备
- **API 集成** - 集成各种 Web 服务和 API
- **数据流处理** - 实时数据处理和转换
- **自动化工作流** - 创建复杂的自动化流程
- **系统监控** - 监控系统状态和发送报警
- **原型开发** - 快速构建概念验证

## 🔧 核心功能

### 可视化编程
- 拖拽式节点连接
- 实时流程编辑
- 可视化调试工具

### 丰富的节点库
- HTTP/HTTPS 节点
- MQTT 节点
- 数据库节点
- 文件操作节点
- 自定义函数节点

### 强大的处理能力
- JavaScript 代码执行
- 数据转换和验证
- 条件分支和循环
- 错误处理和恢复

## 📖 学习路径

### 初学者路径
1. 阅读 [快速入门指南](docs/Node-RED快速入门指南.md)
2. 跟随示例创建简单流程
3. 熟悉基本节点操作
4. 尝试修改和扩展示例

### 进阶学习路径
1. 深入学习 [完整使用指南](docs/Node-RED完整使用指南.md)
2. 参考 [节点参考手册](docs/Node-RED节点参考手册.md)
3. 创建复杂的数据流应用
4. 学习自定义节点开发

## 🛠️ 开发环境

### 系统要求
- Node.js 14.0 或更高版本
- 现代浏览器（Chrome、Firefox、Safari、Edge）
- 至少 512MB 可用内存

### 推荐开发工具
- Visual Studio Code
- Node-RED 编辑器（内置）
- 浏览器开发者工具

## 🔍 常见问题

### 安装问题
**Q: 安装 Node-RED 时出现权限错误**
A: 使用 `sudo npm install -g node-red` 或配置 npm 全局安装路径

**Q: 启动时端口被占用**
A: 修改配置文件中的端口号，或使用 `node-red -p 1881` 指定端口

### 使用问题
**Q: 流程不执行**
A: 检查节点配置、连线状态，确保已点击部署按钮

**Q: 节点显示红色**
A: 查看错误信息，检查配置参数和网络连接

## 📞 技术支持

### 官方资源
- [Node-RED 官方网站](https://nodered.org/)
- [官方文档](https://nodered.org/docs/)
- [社区论坛](https://discourse.nodered.org/)
- [GitHub 仓库](https://github.com/node-red/node-red)

### 社区资源
- [Node-RED 节点库](https://flows.nodered.org/)
- [示例流程](https://flows.nodered.org/)
- [YouTube 教程](https://www.youtube.com/results?search_query=node-red)

## 🤝 贡献指南

欢迎为项目文档贡献内容：

1. Fork 本项目
2. 创建功能分支
3. 提交更改
4. 发起 Pull Request

### 文档规范
- 使用 Markdown 格式
- 保持中文文档风格一致
- 添加适当的代码示例
- 包含截图和图表说明

## 📄 许可证

本项目文档采用 [MIT 许可证](LICENSE) 发布。

## 📝 更新日志

### v1.0.0 (2024-01-XX)
- 创建完整的 Node-RED 使用文档
- 添加快速入门指南
- 提供详细的节点参考手册
- 整合原始参考资料

## 🙏 致谢

感谢以下资源提供的支持：
- Node-RED 官方团队
- 开源社区贡献者
- 原始文档作者

---

**注意**：本文档基于 Node-RED 最新版本编写，如有疑问请参考官方文档或社区支持。

**最后更新**：2024年1月 
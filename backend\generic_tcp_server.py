# /f:/水利站/backend/generic_tcp_server_clean.py
import socket
import threading
import datetime
import json
from data_store import (
    DEVICE_DATA,
    DATA_LOCK,
    CONNECTED_CLIENTS,
    CLIENTS_LOCK,
)
from mysql_connection import MySQLSessionLocal as SessionLocal
import crud
import data_store
from mongodb_models import get_mongodb_manager
from device_control import create_do_command, send_command_to_device
from sqlalchemy.orm import Session
# 已移除设备状态恢复和水泵控制模块
from realtime_data_logger import log_device_data, log_raw_json
from device_status_logger import log_device_status_changes
from rabbitmq_publisher import get_rabbitmq_publisher

# 启动降采样服务
try:
    from data_downsampling_service import start_data_services
    print(f"[{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] [init] 启动数据降采样服务...")
    start_data_services()
    print(f"[{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] [init] 数据降采样服务启动成功")
except Exception as e:
    print(f"[{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] [init] 降采样服务启动失败: {e}")
    print(f"[{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] [init] 系统将继续运行，但不会进行数据降采样")

# 服务器配置
HOST = "0.0.0.0"  # 监听所有可用的网络接口
PORT = 8889  # 监听的端口

# 保留必要的映射常量供安全检查使用
PUMP_TO_DO_MAPPING = {
    "water_pump1": "DO21",
    "water_pump2": "DO22",
}


def _perform_safety_checks(db: Session, sn: str, new_data: dict):
    """
    执行安全检查，适配新的交替启动逻辑。
    新逻辑：同时只应该有一个水泵运行，两个水泵同时运行是异常状态。
    """
    do21_status = new_data.get("DO21_status")
    do22_status = new_data.get("DO22_status")

    # 仅当两个状态都存在且都为1时，触发安全机制
    if do21_status == 1 and do22_status == 1:
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(
            f"[{current_time}] [safety_check] 警告: 检测到水泵DO21和DO22同时开启，这在新的交替启动逻辑中是异常状态"
        )

        # 记录安全检查日志
        safety_log = crud.create_operation_log(
            db=db,
            operation_type="safety_check",
            operation_details="检测到双泵同时运行异常状态，执行安全修复",
            device_sn=sn,
            additional_data={
                "trigger": "dual_pump_running",
                "DO21_status": do21_status,
                "DO22_status": do22_status,
                "detection_time": current_time
            }
        )

        # 检查数据库中应该运行的水泵
        active_pump_key = f"active_pump_{sn}"
        intended_pump = crud.get_kv(db, active_pump_key)
        
        # 根据数据库记录决定保留哪个水泵
        if intended_pump == "water_pump1":
            # 应该运行水泵1，关闭水泵2
            print(f"[{current_time}] [safety_check] 数据库显示应运行水泵1，强制关闭DO22")
            command = create_do_command(do_name="DO22", value=0)
            success, _ = send_command_to_device(sn=sn, command=command)
            
            # 更新日志
            if success:
                crud.update_operation_log_status(db, safety_log.id, "success", f"成功关闭DO22，保留运行水泵1")
            else:
                crud.update_operation_log_status(db, safety_log.id, "failed", f"关闭DO22命令发送失败")
                
        elif intended_pump == "water_pump2":
            # 应该运行水泵2，关闭水泵1
            print(f"[{current_time}] [safety_check] 数据库显示应运行水泵2，强制关闭DO21")
            command = create_do_command(do_name="DO21", value=0)
            success, _ = send_command_to_device(sn=sn, command=command)
            
            # 更新日志
            if success:
                crud.update_operation_log_status(db, safety_log.id, "success", f"成功关闭DO21，保留运行水泵2")
            else:
                crud.update_operation_log_status(db, safety_log.id, "failed", f"关闭DO21命令发送失败")
                
        else:
            # 如果数据库状态不明确，获取下次要使用的水泵信息来决定
            next_pump_key = f"next_pump_to_use_{sn}"
            next_pump = crud.get_kv(db, next_pump_key)
            
            if next_pump == "water_pump2":
                # 下次应该使用水泵2，说明当前应该是水泵1，关闭水泵2
                print(f"[{current_time}] [safety_check] 根据交替逻辑当前应运行水泵1，强制关闭DO22")
                command = create_do_command(do_name="DO22", value=0)
                success, _ = send_command_to_device(sn=sn, command=command)
                
                if success:
                    # 更新数据库状态
                    crud.set_kv(db, active_pump_key, "water_pump1")
                    crud.update_operation_log_status(db, safety_log.id, "success", f"根据交替逻辑保留水泵1，关闭DO22")
                else:
                    crud.update_operation_log_status(db, safety_log.id, "failed", f"关闭DO22命令发送失败")
            else:
                # 默认保留水泵1，关闭水泵2（符合优先启动水泵1的逻辑）
                print(f"[{current_time}] [safety_check] 数据库状态不明确，默认保留DO21，关闭DO22")
                command = create_do_command(do_name="DO22", value=0)
                success, _ = send_command_to_device(sn=sn, command=command)
                
                if success:
                    # 更新数据库状态
                    crud.set_kv(db, active_pump_key, "water_pump1")
                    crud.set_kv(db, next_pump_key, "water_pump2")  # 下次使用水泵2
                    crud.update_operation_log_status(db, safety_log.id, "success", f"默认保留水泵1，关闭DO22，设置下次使用水泵2")
                else:
                    crud.update_operation_log_status(db, safety_log.id, "failed", f"关闭DO22命令发送失败")


def handle_client(client_socket, addr):
    """
    处理客户端连接的函数, 接收数据、解析并存入共享存储区
    :param client_socket: 客户端的socket对象
    :param addr: 客户端地址
    """
    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{current_time}] [handle_client] 接受来自 {addr[0]}:{addr[1]} 的连接")

    # 用于存储此连接的设备SN，以便在断开连接时可以正确清理
    device_sn_for_this_connection = None

    try:
        buffer = ""
        while True:
            # 接收数据
            data = client_socket.recv(1024)
            if not data:
                current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                print(
                    f"[{current_time}] [handle_client] 客户端 {addr[0]}:{addr[1]} 已断开连接"
                )
                break

            # 将接收到的字节解码为字符串并拼接到缓冲区
            buffer += data.decode("utf-8", errors="ignore")

            # 持续尝试从缓冲区中提取并处理完整的JSON对象
            while True:
                start_index = buffer.find("{")
                # 如果缓冲区里连 '{' 都没有，那就没必要继续了
                if start_index == -1:
                    break

                # 从找到的第一个'{'开始，通过计算括号配对来寻找完整的JSON
                brace_count = 0
                end_index = -1
                for i in range(start_index, len(buffer)):
                    if buffer[i] == "{":
                        brace_count += 1
                    elif buffer[i] == "}":
                        brace_count -= 1

                    # 当括号计数回到0时，说明找到了匹配的'}'
                    if brace_count == 0:
                        end_index = i
                        break

                # 如果找到了完整的JSON (end_index有效)
                if end_index != -1:
                    json_str = buffer[start_index : end_index + 1]
                    # 从缓冲区中移除已经处理过的JSON字符串
                    buffer = buffer[end_index + 1 :]

                    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    try:
                        # 解析JSON数据
                        parsed_data = json.loads(json_str)
                        print(
                            f"[{current_time}] [handle_client] 收到来自 {addr[0]}:{addr[1]} 的有效JSON: {json_str}"
                        )
                        
                        # 详细打印接收到的数据内容
                        print(f"\n{'='*60}")
                        print(f"[数据详情] 时间: {current_time}")
                        print(f"[客户端] {addr[0]}:{addr[1]}")
                        print(f"[原始JSON] {json_str}")
                        
                        # 提取并打印关键数据
                        sn = None
                        if "sn" in parsed_data:
                            sn = parsed_data["sn"]
                        elif "data" in parsed_data and isinstance(parsed_data["data"], dict):
                            data_section = parsed_data["data"]
                            if "device_info" in data_section and isinstance(data_section["device_info"], dict):
                                sn = data_section["device_info"].get("sn")
                        elif "device_info" in parsed_data and isinstance(parsed_data["device_info"], dict):
                            sn = parsed_data["device_info"].get("sn")
                        elif "deviceSN" in parsed_data:
                            sn = parsed_data["deviceSN"]
                        
                        if sn:
                            print(f"[设备SN] {sn}")
                        else:
                            print(f"[设备SN] 未找到")
                        
                        # 打印时间戳
                        timestamp = parsed_data.get("timestamp", "无")
                        print(f"[时间戳] {timestamp}")
                        
                        # 打印温湿度数据
                        if "wenshidu" in parsed_data:
                            temp = parsed_data["wenshidu"].get("temperature", "N/A")
                            humidity = parsed_data["wenshidu"].get("humidity", "N/A")
                            print(f"[温湿度] 温度:{temp}°C 湿度:{humidity}%")
                        
                        # 打印电力数据
                        if "diannengbiao" in parsed_data:
                            voltages = parsed_data["diannengbiao"].get("voltages", {})
                            if voltages:
                                ua = voltages.get("Ua", "N/A")
                                ub = voltages.get("Ub", "N/A") 
                                uc = voltages.get("Uc", "N/A")
                                print(f"[电压] Ua:{ua}V Ub:{ub}V Uc:{uc}V")
                        
                        # 打印浮球开关状态
                        if "float_switches" in parsed_data:
                            float1 = parsed_data["float_switches"].get("float1", "N/A")
                            print(f"[浮球开关] float1:{float1}")
                        
                        # 打印DO状态
                        do_states = []
                        for key in ["DO21_status", "DO22_status", "DO23_status", "DO24_status"]:
                            if key in parsed_data:
                                do_states.append(f"{key}:{parsed_data[key]}")
                        if do_states:
                            print(f"[DO状态] {' '.join(do_states)}")
                        
                        # 打印水泵状态
                        pump_states = []
                        for pump_key in ["water_pump1", "water_pump2"]:
                            if pump_key in parsed_data and isinstance(parsed_data[pump_key], dict):
                                auto_status = parsed_data[pump_key].get("auto_status", "N/A")
                                pump_states.append(f"{pump_key}:{auto_status}")
                        if pump_states:
                            print(f"[水泵状态] {' '.join(pump_states)}")
                        
                        print(f"{'='*60}\n")
                        
                        # 记录原始JSON到实时日志（在解析之后立即记录）
                        log_raw_json(json_str, f"{addr[0]}:{addr[1]}")

                        # 从数据中提取设备序列号 'sn'
                        sn = None
                        is_status_only_message = False  # 标记是否为单独的状态变化消息
                        
                        # 检查是否为单独的状态变化消息（只包含状态变化信息，不包含完整设备数据）
                        status_fields = ['DO21_status', 'DO22_status', 'DO23_status', 'DO24_status']
                        pump_fields = ['water_pump1', 'water_pump2', 'air_pump1', 'air_pump2']
                        has_status_fields = any(field in parsed_data for field in status_fields + pump_fields)
                        has_complete_data = 'wenshidu' in parsed_data or 'diannengbiao' in parsed_data
                        
                        if has_status_fields and not has_complete_data:
                            is_status_only_message = True
                            print(f"[{current_time}] [handle_client] 检测到单独的状态变化消息")
                        # 优先检查根级别的sn字段（标准格式）
                        if "sn" in parsed_data:
                            sn = parsed_data["sn"]
                        # 检查data.device_info.sn字段（数据结构中的备用位置）
                        elif "data" in parsed_data and isinstance(parsed_data["data"], dict):
                            data = parsed_data["data"]
                            if "device_info" in data and isinstance(data["device_info"], dict):
                                sn = data["device_info"].get("sn")
                        # 检查device_info.sn字段（M100格式）
                        elif "device_info" in parsed_data and isinstance(
                            parsed_data["device_info"], dict
                        ):
                            sn = parsed_data["device_info"].get("sn")
                        # 检查deviceSN字段（其他格式）
                        elif "deviceSN" in parsed_data:
                            sn = parsed_data["deviceSN"]

                        if sn:
                            # 更新连接信息（在执行控制逻辑之前）
                            if device_sn_for_this_connection is None:
                                device_sn_for_this_connection = sn
                            
                            # 更新连接信息到全局字典，覆盖可能的旧连接
                            with CLIENTS_LOCK:
                                old_client = CONNECTED_CLIENTS.get(sn)
                                if old_client and old_client["socket"] != client_socket:
                                    # 设备重新连接，关闭旧连接
                                    try:
                                        old_client["socket"].close()
                                        print(f"[{current_time}] [handle_client] 设备 {sn} 重新连接，已关闭旧连接")
                                    except:
                                        pass
                                
                                CONNECTED_CLIENTS[sn] = {
                                    "socket": client_socket,
                                    "address": addr,
                                }
                            
                            db = SessionLocal()
                            try:
                                # 提取实际的设备数据（处理嵌套格式）
                                device_data = parsed_data
                                if "data" in parsed_data and isinstance(parsed_data["data"], dict):
                                    # 数据格式：数据在data字段中，将其提取出来与根级别合并
                                    device_data = parsed_data["data"].copy()
                                    # 保留根级别的重要字段
                                    for key in ["sn", "timestamp", "topic"]:
                                        if key in parsed_data:
                                            device_data[key] = parsed_data[key]
                                
                                # 所有设备均采用本地自主控制，不需要后端控制
                                # is_m300_device = True  # 所有设备都是本地自主控制
                                
                                # 已移除所有远程控制逻辑，设备本地自主控制

                                # 记录设备数据到实时日志文件（保存所有原始数据，无降采样）
                                log_device_data(sn, device_data, f"{addr[0]}:{addr[1]}")
                                
                                # 推送设备数据到RabbitMQ消息队列
                                try:
                                    publisher = get_rabbitmq_publisher()
                                    publisher.publish_device_data(sn, device_data, f"{addr[0]}:{addr[1]}")
                                except Exception as e:
                                    print(f"[{current_time}] [rabbitmq] 推送设备数据到RabbitMQ失败: {e}")
                                
                                # 检测并记录设备状态变化到专门的状态变化日志文件
                                status_changes = log_device_status_changes(sn, device_data, f"{addr[0]}:{addr[1]}", is_status_only_message)
                                
                                # 如果有状态变化，推送到RabbitMQ
                                if status_changes:
                                    try:
                                        publisher = get_rabbitmq_publisher()
                                        publisher.publish_status_change(sn, status_changes, f"{addr[0]}:{addr[1]}", is_status_only_message)
                                    except Exception as e:
                                        print(f"[{current_time}] [rabbitmq] 推送状态变化到RabbitMQ失败: {e}")
                                
                                # 1. 更新内存缓存 (用于API快速返回最新状态)
                                with DATA_LOCK:
                                    if sn not in DEVICE_DATA:
                                        DEVICE_DATA[sn] = {}
                                    DEVICE_DATA[sn].update(device_data)
                                    DEVICE_DATA[sn]["last_updated"] = current_time

                                # 2. 将5秒级实时数据存储到MongoDB（内存数据库）
                                try:
                                    mongodb_manager = get_mongodb_manager()
                                    mongodb_manager.insert_device_data(sn, device_data)
                                    print(f"[{current_time}] [handle_client] 设备 {sn} 数据已存储到MongoDB")
                                except Exception as mongo_error:
                                    print(f"[{current_time}] [handle_client] MongoDB存储失败: {mongo_error}")

                                # 3. 将操作日志和系统状态持久化到原有数据库（保持向后兼容）
                                # 注意：这里不再存储设备数据日志，只保留系统功能相关的数据

                                # 服务器仅在收到数据包时回复，避免对指令回执进行回复
                                response = "Server received your message."
                                try:
                                    client_socket.sendall(response.encode("utf-8"))
                                except socket.error as e:
                                    print(
                                        f"[{current_time}] [handle_client] 发送回复失败: {e}."
                                    )
                                    # 发送失败通常意味着连接已断开
                                    break
                            finally:
                                db.close()
                        else:
                            # 如果JSON中没有 'sn', 我们假定它是一个命令执行后的响应包。
                            print(
                                f"[{current_time}] [handle_client] 收到无设备SN的JSON数据，可能是指令回执: {json_str}"
                            )

                    except json.JSONDecodeError as e:
                        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        print(
                            f"[{current_time}] [handle_client] JSON解析失败: {e}, 数据: {json_str}"
                        )
                else:
                    # 如果没有找到完整的JSON，退出内层循环，继续接收更多数据
                    break

    except Exception as e:
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{current_time}] [handle_client] 处理客户端连接时发生错误: {e}")
        import traceback
        traceback.print_exc()

    finally:
        # 在连接结束时，从全局连接字典中移除此设备的连接信息
        if device_sn_for_this_connection:
            with CLIENTS_LOCK:
                reclaimed_client = CONNECTED_CLIENTS.pop(
                    device_sn_for_this_connection, None
                )
                if reclaimed_client:
                    print(
                        f"[{current_time}] [handle_client] 设备 {device_sn_for_this_connection} 的连接信息已从连接池中移除。"
                    )
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(
            f"[{current_time}] [handle_client] 关闭与客户端 {addr[0]}:{addr[1]} 的连接"
        )
        client_socket.close()


def start_server():
    """
    启动TCP服务器的主函数
    """
    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{current_time}] [start_server] 正在启动TCP服务器...")

    # 创建socket
    server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)

    try:
        # 绑定地址和端口
        server_socket.bind((HOST, PORT))
        server_socket.listen(5)  # 允许最多5个挂起的连接

        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{current_time}] [start_server] TCP服务器已启动，监听 {HOST}:{PORT}")

        while True:
            # 接受客户端连接
            client_socket, addr = server_socket.accept()

            # 为每个客户端连接创建一个新线程
            client_thread = threading.Thread(
                target=handle_client, args=(client_socket, addr)
            )
            client_thread.daemon = True  # 设置为守护线程
            client_thread.start()

    except Exception as e:
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{current_time}] [start_server] 服务器启动失败: {e}")
    finally:
        server_socket.close()


if __name__ == "__main__":
    start_server()